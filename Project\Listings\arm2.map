Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_it.o(i.ADC_IRQHandler) refers to gd32f4xx_adc.o(i.adc_interrupt_flag_clear) for adc_interrupt_flag_clear
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_get) for exti_interrupt_flag_get
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to main.o(.data) for fpga_syn
    gd32f4xx_it.o(i.EXTI3_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_get) for exti_interrupt_flag_get
    gd32f4xx_it.o(i.EXTI3_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f4xx_it.o(i.EXTI3_IRQHandler) refers to main.o(.data) for fpga_syn
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_get) for exti_interrupt_flag_get
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to memseta.o(.text) for __aeabi_memclr
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to bsp_fmc.o(i.DRam_Read) for DRam_Read
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to logger.o(i.synthesisLogBuf) for synthesisLogBuf
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to logger.o(i.writeCSVLog) for writeCSVLog
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_data.o(.bss) for hINSFPGAData
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_data.o(.data) for g_LEDIndicatorState
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to main.o(.data) for g_usb_ready
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to main.o(.bss) for g_logFileName
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to memseta.o(.text) for __aeabi_memclr
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to logger.o(i.generateCSVLogFileName) for generateCSVLogFileName
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to bsp_rtc.o(i.timeSync) for timeSync
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to bsp_tim.o(.data) for time_periodic_sec_cnt
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to main.o(.bss) for g_logFileName
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to ins_data.o(.data) for g_second
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to bsp_tim.o(.data) for time_base_periodic_cnt
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to main.o(.data) for g_CAN_Timeout_Start_flag
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to ins_data.o(.data) for g_LEDIndicatorState
    gd32f4xx_it.o(i.UART4_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.UART4_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    gd32f4xx_it.o(i.UART4_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    gd32f4xx_it.o(i.UART4_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_disable) for usart_interrupt_disable
    gd32f4xx_it.o(i.UART4_IRQHandler) refers to gd32f4xx_it.o(.data) for grxlen
    gd32f4xx_it.o(i.UART4_IRQHandler) refers to gd32f4xx_it.o(.bss) for grxbuffer
    gd32f4xx_it.o(i.UART4_IRQHandler) refers to firmwareupdatefile.o(.data) for g_StartUpdateFirm
    gd32f4xx_it.o(i.UART4_IRQHandler) refers to main.o(.data) for goutputmode
    gd32f4xx_it.o(i.UART6_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.UART6_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    gd32f4xx_it.o(i.UART6_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    gd32f4xx_it.o(i.UART6_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_disable) for usart_interrupt_disable
    gd32f4xx_it.o(i.UART6_IRQHandler) refers to gd32f4xx_it.o(.data) for nbr_data_to_read6
    gd32f4xx_it.o(i.UART6_IRQHandler) refers to gd32f4xx_it.o(.bss) for grxbuffer6
    gd32f4xx_it.o(i.UpdateFirmsendmsg) refers to memseta.o(.text) for __aeabi_memclr4
    gd32f4xx_it.o(i.UpdateFirmsendmsg) refers to memcpya.o(.text) for __aeabi_memcpy
    gd32f4xx_it.o(i.UpdateFirmsendmsg) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f4xx_it.o(i.UpdateFirmsendmsg) refers to gd32f4xx_it.o(.data) for gbtxcompleted6
    gd32f4xx_it.o(i.printf_uart4) refers to memseta.o(.text) for __aeabi_memclr
    gd32f4xx_it.o(i.printf_uart4) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    gd32f4xx_it.o(i.printf_uart4) refers to strlen.o(.text) for strlen
    gd32f4xx_it.o(i.printf_uart4) refers to memcpya.o(.text) for __aeabi_memcpy
    gd32f4xx_it.o(i.printf_uart4) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f4xx_it.o(i.printf_uart4) refers to gd32f4xx_it.o(.data) for gbtxcompleted
    gd32f4xx_it.o(i.printf_uart4) refers to gd32f4xx_it.o(.bss) for gprintmsgout
    gd32f4xx_it.o(i.uart4sendmsg) refers to memcpya.o(.text) for __aeabi_memcpy
    gd32f4xx_it.o(i.uart4sendmsg) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f4xx_it.o(i.uart4sendmsg) refers to main.o(.data) for gbilldebuguart4
    gd32f4xx_it.o(i.uart4sendmsg) refers to gd32f4xx_it.o(.data) for gbtxcompleted
    gd32f4xx_it.o(i.uart4sendmsg_billdebug) refers to memcpya.o(.text) for __aeabi_memcpy
    gd32f4xx_it.o(i.uart4sendmsg_billdebug) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f4xx_it.o(i.uart4sendmsg_billdebug) refers to main.o(.data) for gbilldebuguart4
    gd32f4xx_it.o(i.uart4sendmsg_billdebug) refers to gd32f4xx_it.o(.data) for gbtxcompleted
    gd32f4xx_it.o(i.uart6sendmsg) refers to memcpya.o(.text) for __aeabi_memcpy
    gd32f4xx_it.o(i.uart6sendmsg) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f4xx_it.o(i.uart6sendmsg) refers to gd32f4xx_it.o(.data) for gbtxcompleted6
    main.o(i.GetChipID) refers to computerframeparse.o(.bss) for hSetting
    main.o(i.GetChipID) refers to computerframeparse.o(.data) for hDefaultSetting
    main.o(i.INS_Init) refers to systick.o(i.delay_init) for delay_init
    main.o(i.INS_Init) refers to gdwatch.o(i.initializationdriversettings) for initializationdriversettings
    main.o(i.INS_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    main.o(i.INS_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    main.o(i.INS_Init) refers to bsp_gpio.o(i.bsp_gpio_init) for bsp_gpio_init
    main.o(i.INS_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    main.o(i.INS_Init) refers to bsp_tim.o(i.bsp_tim_init) for bsp_tim_init
    main.o(i.INS_Init) refers to bsp_flash.o(i.InitFlashAddr) for InitFlashAddr
    main.o(i.INS_Init) refers to bsp_fmc.o(i.exmc_asynchronous_sram_init) for exmc_asynchronous_sram_init
    main.o(i.INS_Init) refers to bsp_fmc.o(i.Uart_TxInit) for Uart_TxInit
    main.o(i.INS_Init) refers to bsp_fmc.o(i.Uart_RxInit) for Uart_RxInit
    main.o(i.INS_Init) refers to ch378_spi_hw.o(i.mInitCH378Host) for mInitCH378Host
    main.o(i.INS_Init) refers to bsp_rtc.o(i.bsp_rtc_init) for bsp_rtc_init
    main.o(i.INS_Init) refers to bsp_can.o(i.bsp_can_init) for bsp_can_init
    main.o(i.INS_Init) refers to main.o(i.trng_configuration) for trng_configuration
    main.o(i.INS_Init) refers to tcpserver.o(i.TCPServer_Init) for TCPServer_Init
    main.o(i.INS_Init) refers to bsp_exti.o(i.bsp_exti_init) for bsp_exti_init
    main.o(i.INS_Init) refers to bmp280.o(i.bmp280_init) for bmp280_init
    main.o(i.INS_Init) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.INS_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    main.o(i.INS_Init) refers to main.o(i.gd_eval_com_init) for gd_eval_com_init
    main.o(i.INS_Init) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    main.o(i.INS_Init) refers to main.o(i.gd_eval_com_init6) for gd_eval_com_init6
    main.o(i.INS_Init) refers to computerframeparse.o(i.comm_store_init) for comm_store_init
    main.o(i.INS_Init) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    main.o(i.INS_Init) refers to computerframeparse.o(i.comm_send_end_frame) for comm_send_end_frame
    main.o(i.INS_Init) refers to bsp_rtc.o(.data) for pRTC
    main.o(i.INS_Init) refers to bsp_can.o(.data) for hCAN0
    main.o(i.INS_Init) refers to main.o(.data) for g_bmp280_measTime
    main.o(i.INS_Init) refers to ins_data.o(.bss) for hINSData
    main.o(i.INS_Init) refers to gdwatch.o(.bss) for gdriverdatalist
    main.o(i.INS_Init) refers to setparabao.o(.bss) for stSetPara
    main.o(i.LEDIndicator) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    main.o(i.LEDIndicator) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    main.o(i.SetDefaultProductInfo) refers to bsp_flash.o(i.ReadFlash) for ReadFlash
    main.o(i.SetDefaultProductInfo) refers to main.o(i.GetChipID) for GetChipID
    main.o(i.SetDefaultProductInfo) refers to bsp_flash.o(i.WriteFlash) for WriteFlash
    main.o(i.SetDefaultProductInfo) refers to computerframeparse.o(.bss) for hSetting
    main.o(i.SetDefaultProductInfo) refers to computerframeparse.o(.data) for hDefaultSetting
    main.o(i.StartNavigation) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    main.o(i.StartNavigation) refers to systick.o(i.delay_us) for delay_us
    main.o(i.StartNavigation) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    main.o(i.calcGPRMC_TRA) refers to dflti.o(.text) for __aeabi_i2d
    main.o(i.calcGPRMC_TRA) refers to dmul.o(.text) for __aeabi_dmul
    main.o(i.calcGPRMC_TRA) refers to d2f.o(.text) for __aeabi_d2f
    main.o(i.calcGPRMC_TRA) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.checkUSBReady) refers to file_sys.o(i.CH378DiskReady) for CH378DiskReady
    main.o(i.checkUSBReady) refers to file_sys.o(i.CH378GetDiskStatus) for CH378GetDiskStatus
    main.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    main.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    main.o(i.gd_eval_com_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    main.o(i.gd_eval_com_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    main.o(i.gd_eval_com_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    main.o(i.gd_eval_com_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    main.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    main.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    main.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    main.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    main.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_word_length_set) for usart_word_length_set
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_parity_config) for usart_parity_config
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    main.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_flag_clear) for usart_flag_clear
    main.o(i.get_16bit_D32) refers to main.o(.data) for m16_uMemory
    main.o(i.get_16bit_D64) refers to main.o(.data) for m16_uMemory
    main.o(i.get_16bit_Int32) refers to main.o(.data) for m16_uMemory
    main.o(i.get_fpgadata) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.get_fpgadata) refers to memcpya.o(.text) for __aeabi_memcpy
    main.o(i.get_fpgadata) refers to dflti.o(.text) for __aeabi_i2d
    main.o(i.get_fpgadata) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(i.get_fpgadata) refers to dadd.o(.text) for __aeabi_dadd
    main.o(i.get_fpgadata) refers to dfixi.o(.text) for __aeabi_d2iz
    main.o(i.get_fpgadata) refers to main.o(.bss) for gfpgadata
    main.o(i.get_fpgadata) refers to fpgad.o(.bss) for gpagedata
    main.o(i.get_fpgadata) refers to gnss.o(.bss) for hGPSData
    main.o(i.get_fpgadata) refers to main.o(.data) for gpsssecond0
    main.o(i.get_fpgadata) refers to ins_data.o(.bss) for hINSCANData
    main.o(i.get_fpgadata) refers to main.o(i.get_16bit_Int32) for get_16bit_Int32
    main.o(i.get_fpgadata) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(i.get_fpgadata) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    main.o(i.get_fpgadata) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    main.o(i.get_fpgadata) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.get_fpgadata) refers to strlen.o(.text) for strlen
    main.o(i.get_fpgadata) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    main.o(i.get_fpgadata) refers to main.o(i.get_16bit_D32) for get_16bit_D32
    main.o(i.get_fpgadata) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.get_fpgadata) refers to main.o(i.get_16bit_D64) for get_16bit_D64
    main.o(i.get_fpgadata) refers to d2f.o(.text) for __aeabi_d2f
    main.o(i.get_fpgadata) refers to main.o(.conststring) for .conststring
    main.o(i.get_fpgadata) refers to main.o(i.calcGPRMC_TRA) for calcGPRMC_TRA
    main.o(i.get_fpgadata) refers to frame_analysis.o(i.frame_form) for frame_form
    main.o(i.get_fpgadata) refers to ins_output.o(.data) for gfog0
    main.o(i.get_fpgadata) refers to gdwatch.o(.data) for gsystemflag
    main.o(i.loggingLogFile) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.loggingLogFile) refers to logger.o(i.generateCSVLogFileName) for generateCSVLogFileName
    main.o(i.loggingLogFile) refers to logger.o(i.writeCSVLog) for writeCSVLog
    main.o(i.main) refers to gd32f4xx_misc.o(i.nvic_vector_table_set) for nvic_vector_table_set
    main.o(i.main) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    main.o(i.main) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    main.o(i.main) refers to setparabao.o(i.ReadParaFromFlash) for ReadParaFromFlash
    main.o(i.main) refers to main.o(i.INS_Init) for INS_Init
    main.o(i.main) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    main.o(i.main) refers to strcpy.o(.text) for strcpy
    main.o(i.main) refers to strlen.o(.text) for strlen
    main.o(i.main) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    main.o(i.main) refers to gd32f4xx_it.o(i.uart6sendmsg) for uart6sendmsg
    main.o(i.main) refers to main.o(i.get_fpgadata) for get_fpgadata
    main.o(i.main) refers to computerframeparse.o(i.wheel_is_running) for wheel_is_running
    main.o(i.main) refers to nav_app.o(i.NAV_function) for NAV_function
    main.o(i.main) refers to ins_output.o(i.NAV_Output) for NAV_Output
    main.o(i.main) refers to bsp_can.o(i.uart4sendmsg_canout) for uart4sendmsg_canout
    main.o(i.main) refers to main.o(i.LEDIndicator) for LEDIndicator
    main.o(i.main) refers to protocol.o(i.analysisRxdata) for analysisRxdata
    main.o(i.main) refers to firmwareupdatefile.o(i.UartDataHandle) for UartDataHandle
    main.o(i.main) refers to main.o(.data) for goutputmode
    main.o(i.main) refers to ins_data.o(.data) for g_LEDIndicatorState
    main.o(i.main) refers to main.o(.bss) for combineData
    main.o(i.main) refers to nav_app.o(.bss) for NAV_Data_Full
    main.o(i.main) refers to bsp_can.o(.bss) for gCanRxBuf
    main.o(i.main) refers to gd32f4xx_it.o(.data) for g_Uart6_Rx_Finish
    main.o(i.trng_configuration) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    main.o(i.trng_configuration) refers to gd32f4xx_trng.o(i.trng_deinit) for trng_deinit
    main.o(i.trng_configuration) refers to gd32f4xx_trng.o(i.trng_enable) for trng_enable
    main.o(i.trng_configuration) refers to main.o(i.trng_ready_check) for trng_ready_check
    main.o(i.trng_ready_check) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    main.o(.data) refers to main.o(.conststring) for .conststring
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.delay_init) refers to gd32f4xx_misc.o(i.systick_clksource_set) for systick_clksource_set
    systick.o(i.delay_init) refers to systick.o(.data) for fac_us
    systick.o(i.delay_ms) refers to systick.o(.data) for delay
    systick.o(i.delay_us) refers to systick.o(.data) for fac_us
    systick.o(i.delay_xms) refers to systick.o(i.delay_us) for delay_us
    systick.o(i.systick_config) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_RMC_Buff_Parser) for gnss_RMC_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_GGA_Buff_Parser) for gnss_GGA_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_VTG_Buff_Parser) for gnss_VTG_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_ZDA_Buff_Parser) for gnss_ZDA_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_Heading_Buff_Parser) for gnss_Heading_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_BESTPOS_Buff_Parser) for gnss_BESTPOS_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_BESTVEL_Buff_Parser) for gnss_BESTVEL_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_TRA_Buff_Parser) for gnss_TRA_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_AGRIC_Buff_Parser) for gnss_AGRIC_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(.data) for gCmdTypeIndex
    gnss.o(i.GNSS_Cmd_Parser) refers to strncmp.o(.text) for strncmp
    gnss.o(i.GNSS_Cmd_Parser) refers to gnss.o(.data) for gCmdTypeIndex
    gnss.o(i.gnss_AGRIC_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_AGRIC_Buff_Parser) refers to atoi.o(.text) for atoi
    gnss.o(i.gnss_AGRIC_Buff_Parser) refers to strncpy.o(.text) for strncpy
    gnss.o(i.gnss_AGRIC_Buff_Parser) refers to atol.o(.text) for atol
    gnss.o(i.gnss_AGRIC_Buff_Parser) refers to strtod.o(i.__hardfp_strtod) for __hardfp_strtod
    gnss.o(i.gnss_AGRIC_Buff_Parser) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_AGRIC_Buff_Parser) refers to gnss.o(.data) for gCmdIndex
    gnss.o(i.gnss_AGRIC_Buff_Parser) refers to gnss.o(.bss) for hGPSAgricData
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to strncmp.o(.text) for strncmp
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to atol.o(.text) for atol
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to strchr.o(.text) for strchr
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to atoi.o(.text) for atoi
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to gnss.o(.data) for gCmdIndex
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to gnss.o(.bss) for hGPSBestPosData
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to gnss.o(.constdata) for sol_status
    gnss.o(i.gnss_BESTVEL_Buff_Parser) refers to strncmp.o(.text) for strncmp
    gnss.o(i.gnss_BESTVEL_Buff_Parser) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gnss.o(i.gnss_BESTVEL_Buff_Parser) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_BESTVEL_Buff_Parser) refers to atol.o(.text) for atol
    gnss.o(i.gnss_BESTVEL_Buff_Parser) refers to strchr.o(.text) for strchr
    gnss.o(i.gnss_BESTVEL_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_BESTVEL_Buff_Parser) refers to strtod.o(i.__hardfp_strtod) for __hardfp_strtod
    gnss.o(i.gnss_BESTVEL_Buff_Parser) refers to gnss.o(.data) for gCmdIndex
    gnss.o(i.gnss_BESTVEL_Buff_Parser) refers to gnss.o(.bss) for hGPSBestVelData
    gnss.o(i.gnss_BESTVEL_Buff_Parser) refers to gnss.o(.constdata) for sol_status
    gnss.o(i.gnss_Fetch_Data) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_Fetch_Data) refers to f2d.o(.text) for __aeabi_f2d
    gnss.o(i.gnss_Fetch_Data) refers to gnss.o(.bss) for hGPSRmcData
    gnss.o(i.gnss_GGA_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_GGA_Buff_Parser) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gnss.o(i.gnss_GGA_Buff_Parser) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_GGA_Buff_Parser) refers to atoi.o(.text) for atoi
    gnss.o(i.gnss_GGA_Buff_Parser) refers to gnss.o(.data) for gCmdIndex
    gnss.o(i.gnss_GGA_Buff_Parser) refers to gnss.o(.bss) for hGPSGgaData
    gnss.o(i.gnss_Heading_Buff_Parser) refers to strncmp.o(.text) for strncmp
    gnss.o(i.gnss_Heading_Buff_Parser) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gnss.o(i.gnss_Heading_Buff_Parser) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_Heading_Buff_Parser) refers to atol.o(.text) for atol
    gnss.o(i.gnss_Heading_Buff_Parser) refers to strchr.o(.text) for strchr
    gnss.o(i.gnss_Heading_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_Heading_Buff_Parser) refers to atoi.o(.text) for atoi
    gnss.o(i.gnss_Heading_Buff_Parser) refers to gnss.o(.data) for gCmdIndex
    gnss.o(i.gnss_Heading_Buff_Parser) refers to gnss.o(.bss) for hGPSHeadingData
    gnss.o(i.gnss_Heading_Buff_Parser) refers to gnss.o(.constdata) for sol_status
    gnss.o(i.gnss_RMC_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_RMC_Buff_Parser) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gnss.o(i.gnss_RMC_Buff_Parser) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_RMC_Buff_Parser) refers to atol.o(.text) for atol
    gnss.o(i.gnss_RMC_Buff_Parser) refers to gnss.o(.data) for gCmdIndex
    gnss.o(i.gnss_RMC_Buff_Parser) refers to gnss.o(.bss) for hGPSRmcData
    gnss.o(i.gnss_TRA_Buff_Parser) refers to strncmp.o(.text) for strncmp
    gnss.o(i.gnss_TRA_Buff_Parser) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gnss.o(i.gnss_TRA_Buff_Parser) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_TRA_Buff_Parser) refers to atoi.o(.text) for atoi
    gnss.o(i.gnss_TRA_Buff_Parser) refers to gnss.o(.data) for gCmdIndex
    gnss.o(i.gnss_TRA_Buff_Parser) refers to gnss.o(.bss) for hGPSTraData
    gnss.o(i.gnss_VTG_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_VTG_Buff_Parser) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gnss.o(i.gnss_VTG_Buff_Parser) refers to gnss.o(.data) for gCmdIndex
    gnss.o(i.gnss_VTG_Buff_Parser) refers to gnss.o(.bss) for hGPSVtgData
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to memseta.o(.text) for __aeabi_memclr4
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to strncpy.o(.text) for strncpy
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to atoi.o(.text) for atoi
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to printfa.o(i.__0sprintf) for __2sprintf
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to strlen.o(.text) for strlen
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to time_unify.o(i.str2time) for str2time
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to time_unify.o(i.time2gpst) for time2gpst
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to dfixui.o(.text) for __aeabi_d2uiz
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to dflti.o(.text) for __aeabi_i2d
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to gnss.o(.data) for gCmdIndex
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to gnss.o(.bss) for hGPSZdaData
    gnss.o(i.gnss_fill_data) refers to gnss.o(i.gnss_parse) for gnss_parse
    gnss.o(i.gnss_fill_data) refers to gnss.o(i.gnss_Fetch_Data) for gnss_Fetch_Data
    gnss.o(i.gnss_fill_data) refers to transplant.o(i.rtc_update) for rtc_update
    gnss.o(i.gnss_fill_data) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_fill_data) refers to dmul.o(.text) for __aeabi_dmul
    gnss.o(i.gnss_fill_data) refers to memcpya.o(.text) for __aeabi_memcpy4
    gnss.o(i.gnss_fill_data) refers to gnss.o(.data) for gnssSynFlg
    gnss.o(i.gnss_fill_data) refers to gnss.o(.bss) for hGPSGgaData
    gnss.o(i.gnss_fill_data) refers to frame_analysis.o(.bss) for navi_test_info
    gnss.o(i.gnss_fill_data) refers to main.o(.bss) for combineData
    gnss.o(i.gnss_get_algorithm_dataPtr) refers to gnss.o(.bss) for hGPSAgricData
    gnss.o(i.gnss_get_baseline) refers to gnss.o(.bss) for hGPSHeadingData
    gnss.o(i.gnss_isLocation) refers to gnss.o(.bss) for hGPSRmcData
    gnss.o(i.gnss_parse) refers to gnss.o(i.GNSS_Cmd_Kind_Parser) for GNSS_Cmd_Kind_Parser
    gnss.o(i.gnss_parse) refers to gnss.o(i.GNSS_Cmd_Parser) for GNSS_Cmd_Parser
    gnss.o(i.gnss_parse) refers to gnss.o(i.gnss_gprmcIsLocation) for gnss_gprmcIsLocation
    gnss.o(i.gnss_parse) refers to strtok.o(.text) for strtok
    gnss.o(i.gnss_parse) refers to gnss.o(i.GNSS_Buff_Parser) for GNSS_Buff_Parser
    gnss.o(i.gnss_parse) refers to gnss.o(.data) for gCmdIndex
    gnss.o(i.gnss_set_ins_offset) refers to memseta.o(.text) for __aeabi_memclr4
    gnss.o(i.gnss_set_ins_offset) refers to printfa.o(i.__0sprintf) for __2sprintf
    gnss.o(i.gnss_set_leverArm) refers to memseta.o(.text) for __aeabi_memclr4
    gnss.o(i.gnss_set_leverArm) refers to printfa.o(i.__0sprintf) for __2sprintf
    gnss.o(i.gnss_set_leverArm) refers to strlen.o(.text) for strlen
    gnss.o(i.gnss_set_leverArm) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    gnss.o(i.gnss_set_posture) refers to memseta.o(.text) for __aeabi_memclr4
    gnss.o(i.gnss_set_posture) refers to printfa.o(i.__0sprintf) for __2sprintf
    gnss.o(i.gnss_timeWrToArm2) refers to bsp_fmc.o(i.DRam_Write) for DRam_Write
    gnss.o(i.gnss_timeWrToArm2) refers to gnss.o(.bss) for hGPSRmcData
    gnss.o(i.gnss_timeWrToArm2) refers to gnss.o(.data) for hGPSTimeSyn
    gnss.o(i.gnss_time_is_valid) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_time_is_valid) refers to gnss.o(.bss) for hGPSAgricData
    gnss.o(i.strtok_test) refers to strtok.o(.text) for strtok
    gnss.o(.constdata) refers to gnss.o(.conststring) for .conststring
    ins_data.o(i.read_flash) refers to bsp_flash.o(i.InitFlashAddr) for InitFlashAddr
    ins_data.o(i.read_flash) refers to bsp_flash.o(i.ReadFlash) for ReadFlash
    ins_data.o(i.read_flash) refers to computerframeparse.o(.bss) for hSetting
    ins_data.o(i.save_flash) refers to bsp_flash.o(i.InitFlashAddr) for InitFlashAddr
    ins_data.o(i.save_flash) refers to bsp_flash.o(i.WriteFlash) for WriteFlash
    ins_data.o(i.save_flash) refers to bsp_flash.o(i.EndWrite) for EndWrite
    ins_data.o(i.save_flash) refers to computerframeparse.o(.bss) for hSetting
    tcpserver.o(i.CH395GlobalInterrupt) refers to ch395cmd.o(i.CH395CMDGetGlobIntStatus_ALL) for CH395CMDGetGlobIntStatus_ALL
    tcpserver.o(i.CH395GlobalInterrupt) refers to tcpserver.o(i.CH395SocketInterrupt) for CH395SocketInterrupt
    tcpserver.o(i.CH395Init) refers to ch395cmd.o(i.CH395CMDCheckExist) for CH395CMDCheckExist
    tcpserver.o(i.CH395Init) refers to ch395cmd.o(i.CH395CMDSetIPAddr) for CH395CMDSetIPAddr
    tcpserver.o(i.CH395Init) refers to ch395cmd.o(i.CH395CMDSetGWIPAddr) for CH395CMDSetGWIPAddr
    tcpserver.o(i.CH395Init) refers to ch395cmd.o(i.CH395CMDSetMASKAddr) for CH395CMDSetMASKAddr
    tcpserver.o(i.CH395Init) refers to ch395cmd.o(i.CH395SetStartPara) for CH395SetStartPara
    tcpserver.o(i.CH395Init) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    tcpserver.o(i.CH395Init) refers to ch395cmd.o(i.CH395CMDInitCH395) for CH395CMDInitCH395
    tcpserver.o(i.CH395Init) refers to tcpserver.o(.bss) for CH395Inf
    tcpserver.o(i.CH395SocketInitOpen) refers to ch395cmd.o(i.CH395SetSocketProtType) for CH395SetSocketProtType
    tcpserver.o(i.CH395SocketInitOpen) refers to ch395cmd.o(i.CH395SetSocketSourPort) for CH395SetSocketSourPort
    tcpserver.o(i.CH395SocketInitOpen) refers to ch395cmd.o(i.CH395OpenSocket) for CH395OpenSocket
    tcpserver.o(i.CH395SocketInitOpen) refers to ch395cmd.o(i.CH395TCPListen) for CH395TCPListen
    tcpserver.o(i.CH395SocketInitOpen) refers to tcpserver.o(.bss) for SockInf
    tcpserver.o(i.CH395SocketInterrupt) refers to ch395cmd.o(i.CH395GetSocketInt) for CH395GetSocketInt
    tcpserver.o(i.CH395SocketInterrupt) refers to ch395cmd.o(i.CH395GetRecvLength) for CH395GetRecvLength
    tcpserver.o(i.CH395SocketInterrupt) refers to ch395cmd.o(i.CH395GetRecvData) for CH395GetRecvData
    tcpserver.o(i.CH395SocketInterrupt) refers to ch395cmd.o(i.CH395SendData) for CH395SendData
    tcpserver.o(i.CH395SocketInterrupt) refers to ch395cmd.o(i.CH395CMDGetRemoteIPP) for CH395CMDGetRemoteIPP
    tcpserver.o(i.CH395SocketInterrupt) refers to ch395cmd.o(i.CH395CloseSocket) for CH395CloseSocket
    tcpserver.o(i.CH395SocketInterrupt) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    tcpserver.o(i.CH395SocketInterrupt) refers to ch395cmd.o(i.CH395OpenSocket) for CH395OpenSocket
    tcpserver.o(i.CH395SocketInterrupt) refers to tcpserver.o(.bss) for MyBuffer
    tcpserver.o(i.InitCH395InfParam) refers to memseta.o(.text) for __aeabi_memclr4
    tcpserver.o(i.InitCH395InfParam) refers to tcpserver.o(.bss) for CH395Inf
    tcpserver.o(i.InitCH395InfParam) refers to tcpserver.o(.constdata) for CH395IPAddr
    tcpserver.o(i.InitSocketParam) refers to memseta.o(.text) for __aeabi_memclr4
    tcpserver.o(i.InitSocketParam) refers to tcpserver.o(.bss) for SockInf
    tcpserver.o(i.InitSocketParam) refers to tcpserver.o(.data) for SocketServerPort
    tcpserver.o(i.PHY_IsConnect) refers to ch395cmd.o(i.CH395CMDGetPHYStatus) for CH395CMDGetPHYStatus
    tcpserver.o(i.PHY_IsConnect) refers to systick.o(i.delay_ms) for delay_ms
    tcpserver.o(i.TCPServer_Init) refers to ch395spi.o(i.CH395_PORT_INIT) for CH395_PORT_INIT
    tcpserver.o(i.TCPServer_Init) refers to ch395spi.o(i.CH395_RST) for CH395_RST
    tcpserver.o(i.TCPServer_Init) refers to tcpserver.o(i.InitCH395InfParam) for InitCH395InfParam
    tcpserver.o(i.TCPServer_Init) refers to tcpserver.o(i.CH395Init) for CH395Init
    tcpserver.o(i.TCPServer_Init) refers to tcpserver.o(i.socket_buffer_config) for socket_buffer_config
    tcpserver.o(i.TCPServer_Init) refers to ch395cmd.o(i.CH395SetTCPMss) for CH395SetTCPMss
    tcpserver.o(i.socket_buffer_config) refers to ch395cmd.o(i.CH395SetSocketRecvBuf) for CH395SetSocketRecvBuf
    tcpserver.o(i.socket_buffer_config) refers to ch395cmd.o(i.CH395SetSocketSendBuf) for CH395SetSocketSendBuf
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.gpst2time) for gpst2time
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.bdt2gpst) for bdt2gpst
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.gpst2utc) for gpst2utc
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.bdt2gpst) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.bdt2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.bdt2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.bdt2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.bdt2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.bdt2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.bdt2time) refers to time_unify.o(.data) for bdt0
    time_unify.o(i.epoch2time) refers to memcpya.o(.text) for __aeabi_memcpy4
    time_unify.o(i.epoch2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.epoch2time) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    time_unify.o(i.epoch2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.epoch2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.epoch2time) refers to time_unify.o(.constdata) for .constdata
    time_unify.o(i.gpst2bdt) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.gpst2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.gpst2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.gpst2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.gpst2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.gpst2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.gpst2time) refers to time_unify.o(.data) for gpst0
    time_unify.o(i.gpst2utc) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.gpst2utc) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.gpst2utc) refers to time_unify.o(i.timediff) for timediff
    time_unify.o(i.gpst2utc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    time_unify.o(i.gpst2utc) refers to time_unify.o(.data) for leaps
    time_unify.o(i.gst2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.gst2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.gst2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.gst2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.gst2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.gst2time) refers to time_unify.o(.data) for gst0
    time_unify.o(i.sow2Date) refers to time_unify.o(i.gst2time) for gst2time
    time_unify.o(i.sow2Date) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.sow2Date) refers to printfa.o(i.__0sprintf) for __2sprintf
    time_unify.o(i.str2time) refers to scanf_fp.o(.text) for _scanf_real
    time_unify.o(i.str2time) refers to strlen.o(.text) for strlen
    time_unify.o(i.str2time) refers to __0sscanf.o(.text) for __0sscanf
    time_unify.o(i.str2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.str2time) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.str2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2bdt) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2bdt) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.time2bdt) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2bdt) refers to time_unify.o(.data) for bdt0
    time_unify.o(i.time2epoch) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.time2epoch) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2epoch) refers to time_unify.o(.data) for mday
    time_unify.o(i.time2gpst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2gpst) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.time2gpst) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2gpst) refers to time_unify.o(.data) for gpst0
    time_unify.o(i.time2gst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2gst) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.time2gst) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2gst) refers to time_unify.o(.data) for gst0
    time_unify.o(i.time2sec) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.time2sec) refers to dmul.o(.text) for __aeabi_dmul
    time_unify.o(i.time2sec) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2sec) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2str) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.time2str) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    time_unify.o(i.time2str) refers to ddiv.o(.text) for __aeabi_ddiv
    time_unify.o(i.time2str) refers to dadd.o(.text) for __aeabi_dsub
    time_unify.o(i.time2str) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.time2str) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.time2str) refers to printfa.o(i.__0sprintf) for __2sprintf
    time_unify.o(i.timeadd) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.timeadd) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    time_unify.o(i.timeadd) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.timediff) refers to dadd.o(.text) for __aeabi_dsub
    time_unify.o(i.timediff) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.utc2gmst) refers to memcpya.o(.text) for __aeabi_memcpy4
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.time2sec) for time2sec
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.timediff) for timediff
    time_unify.o(i.utc2gmst) refers to ddiv.o(.text) for __aeabi_ddiv
    time_unify.o(i.utc2gmst) refers to dmul.o(.text) for __aeabi_dmul
    time_unify.o(i.utc2gmst) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.utc2gmst) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    time_unify.o(i.utc2gmst) refers to time_unify.o(.constdata) for .constdata
    time_unify.o(i.utc2gpst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.utc2gpst) refers to time_unify.o(i.timediff) for timediff
    time_unify.o(i.utc2gpst) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    time_unify.o(i.utc2gpst) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.utc2gpst) refers to time_unify.o(.data) for leaps
    ymodem.o(i.CRC16) refers to ymodem.o(.constdata) for ccitt_table
    ymodem.o(i._rym_do_fin) refers to ymodem.o(i._rym_putchar) for _rym_putchar
    ymodem.o(i._rym_do_fin) refers to ymodem.o(i._rym_read_code) for _rym_read_code
    ymodem.o(i._rym_do_fin) refers to ymodem.o(i._rym_read_data) for _rym_read_data
    ymodem.o(i._rym_do_fin) refers to ymodem.o(i.CRC16) for CRC16
    ymodem.o(i._rym_do_handshake) refers to ymodem.o(i._rym_putchar) for _rym_putchar
    ymodem.o(i._rym_do_handshake) refers to ymodem.o(i._rym_read_data) for _rym_read_data
    ymodem.o(i._rym_do_handshake) refers to strstr.o(.text) for strstr
    ymodem.o(i._rym_do_handshake) refers to strtod.o(i.__hardfp_strtod) for __hardfp_strtod
    ymodem.o(i._rym_do_handshake) refers to dfixui.o(.text) for __aeabi_d2uiz
    ymodem.o(i._rym_do_handshake) refers to ymodem.o(i.CRC16) for CRC16
    ymodem.o(i._rym_do_recv) refers to malloc.o(i.malloc) for malloc
    ymodem.o(i._rym_do_recv) refers to ymodem.o(i._rym_do_handshake) for _rym_do_handshake
    ymodem.o(i._rym_do_recv) refers to ymodem.o(i._rym_do_trans) for _rym_do_trans
    ymodem.o(i._rym_do_recv) refers to ymodem.o(i._rym_do_fin) for _rym_do_fin
    ymodem.o(i._rym_do_trans) refers to bsp_fmc.o(i.fmc_erase_sector_by_address) for fmc_erase_sector_by_address
    ymodem.o(i._rym_do_trans) refers to ymodem.o(i._rym_putchar) for _rym_putchar
    ymodem.o(i._rym_do_trans) refers to ymodem.o(i._rym_trans_data) for _rym_trans_data
    ymodem.o(i._rym_do_trans) refers to systick.o(i.delay_ms) for delay_ms
    ymodem.o(i._rym_do_trans) refers to bsp_fmc.o(i.fmc_write_8bit_data1) for fmc_write_8bit_data1
    ymodem.o(i._rym_putchar) refers to ymodem.o(i.ins_device_write) for ins_device_write
    ymodem.o(i._rym_read_code) refers to ymodem.o(i.ins_device_read) for ins_device_read
    ymodem.o(i._rym_read_code) refers to clock.o(i.ins_tick_get) for ins_tick_get
    ymodem.o(i._rym_read_code) refers to clock.o(i.ins_tick_from_millisecond) for ins_tick_from_millisecond
    ymodem.o(i._rym_read_code) refers to clock.o(i.ins_tick_timeout) for ins_tick_timeout
    ymodem.o(i._rym_read_data) refers to clock.o(i.ins_tick_get) for ins_tick_get
    ymodem.o(i._rym_read_data) refers to clock.o(i.ins_tick_from_millisecond) for ins_tick_from_millisecond
    ymodem.o(i._rym_read_data) refers to clock.o(i.ins_tick_timeout) for ins_tick_timeout
    ymodem.o(i._rym_read_data) refers to gd32f4xx_it.o(.data) for grxlen
    ymodem.o(i._rym_read_data) refers to gd32f4xx_it.o(.bss) for grxbuffer
    ymodem.o(i._rym_trans_data) refers to ymodem.o(i._rym_read_data) for _rym_read_data
    ymodem.o(i._rym_trans_data) refers to ymodem.o(i.CRC16) for CRC16
    ymodem.o(i.ins_device_read) refers to gd32f4xx_it.o(.data) for grxlen
    ymodem.o(i.ins_device_read) refers to gd32f4xx_it.o(.bss) for grxbuffer
    ymodem.o(i.ins_device_write) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    ymodem.o(i.rym_fm_update) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    ymodem.o(i.rym_fm_update) refers to gd32f4xx_misc.o(i.nvic_irq_disable) for nvic_irq_disable
    ymodem.o(i.rym_fm_update) refers to main.o(i.sys_irq_stop) for sys_irq_stop
    ymodem.o(i.rym_fm_update) refers to clock.o(i.ins_tick_get) for ins_tick_get
    ymodem.o(i.rym_fm_update) refers to ymodem.o(i.rym_null) for rym_null
    ymodem.o(i.rym_fm_update) refers to systick.o(i.delay_1ms) for delay_1ms
    ymodem.o(i.rym_fm_update) refers to clock.o(i.ins_tick_from_millisecond) for ins_tick_from_millisecond
    ymodem.o(i.rym_fm_update) refers to clock.o(i.ins_tick_timeout) for ins_tick_timeout
    ymodem.o(i.rym_fm_update) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    ymodem.o(i.rym_fm_update) refers to main.o(i.sys_irq_restart) for sys_irq_restart
    ymodem.o(i.rym_fm_update) refers to computerframeparse.o(i.comm_output_enable) for comm_output_enable
    ymodem.o(i.rym_null) refers to ymodem.o(i.rym_recv_on_device) for rym_recv_on_device
    ymodem.o(i.rym_null) refers to ymodem.o(i._rym_dummy_write) for _rym_dummy_write
    ymodem.o(i.rym_recv_on_device) refers to ymodem.o(i._rym_do_recv) for _rym_do_recv
    ymodem.o(i.rym_recv_on_device) refers to bsp_fmc.o(i.fmc_erase_sector_by_address) for fmc_erase_sector_by_address
    ymodem.o(i.rym_recv_on_device) refers to bsp_fmc.o(i.fmc_write_8bit_data) for fmc_write_8bit_data
    ymodem.o(i.rym_recv_on_device) refers to malloc.o(i.free) for free
    clock.o(i.ins_tick_get) refers to clock.o(.data) for ins_tick
    clock.o(i.ins_tick_increase) refers to clock.o(.data) for ins_tick
    clock.o(i.ins_tick_set) refers to clock.o(.data) for ins_tick
    clock.o(i.ins_tick_timeout) refers to clock.o(i.ins_tick_get) for ins_tick_get
    data_shift.o(i.ProcessIMUDataUserAxis) refers to computerframeparse.o(.bss) for hSetting
    gdwatch.o(i.initializationdriversettings) refers to memseta.o(.text) for __aeabi_memclr
    gdwatch.o(i.initializationdriversettings) refers to strcpy.o(.text) for strcpy
    gdwatch.o(i.initializationdriversettings) refers to gdwatch.o(.bss) for gdriversettings
    gdwatch.o(i.mcusendtopcdriversdata) refers to memcpya.o(.text) for __aeabi_memcpy
    gdwatch.o(i.mcusendtopcdriversdata) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    gdwatch.o(i.mcusendtopcdriversdata) refers to gdwatch.o(.bss) for gdriverdatalist
    ins_output.o(i.NAV_Output) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_output.o(i.NAV_Output) refers to dadd.o(.text) for __aeabi_dadd
    ins_output.o(i.NAV_Output) refers to ddiv.o(.text) for __aeabi_ddiv
    ins_output.o(i.NAV_Output) refers to dfixi.o(.text) for __aeabi_d2iz
    ins_output.o(i.NAV_Output) refers to dmul.o(.text) for __aeabi_dmul
    ins_output.o(i.NAV_Output) refers to gdwatch.o(.data) for gsystemflag
    ins_output.o(i.NAV_Output) refers to gnss.o(.bss) for hGPSData01
    ins_output.o(i.NAV_Output) refers to main.o(.bss) for combineData
    ins_output.o(i.NAV_Output) refers to ins_data.o(.bss) for hINSCANData
    ins_output.o(i.NAV_Output) refers to nav_app.o(.bss) for NAV_Data_Out
    ins_output.o(i.NAV_Output) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    ins_output.o(i.NAV_Output) refers to computerframeparse.o(i.protocol_send) for protocol_send
    ins_output.o(i.NAV_Output) refers to gdwatch.o(i.mcusendtopcdriversdata) for mcusendtopcdriversdata
    ins_output.o(i.NAV_Output) refers to ins_output.o(i.protocol_opticalgyro) for protocol_opticalgyro
    ins_output.o(i.NAV_Output) refers to computerframeparse.o(i.comm_read_currentFreq) for comm_read_currentFreq
    ins_output.o(i.NAV_Output) refers to main.o(.data) for goutputmode
    ins_output.o(i.NAV_Output) refers to ins_output.o(.data) for syncount
    ins_output.o(i.protocol_opticalgyro) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_output.o(.data) refers to ins_output.o(.conststring) for .conststring
    firmwareupdatefile.o(i.Drv_FlashErase) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    firmwareupdatefile.o(i.Drv_FlashErase) refers to memcpya.o(.text) for __aeabi_memcpy4
    firmwareupdatefile.o(i.Drv_FlashErase) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    firmwareupdatefile.o(i.Drv_FlashErase) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    firmwareupdatefile.o(i.Drv_FlashErase) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    firmwareupdatefile.o(i.Drv_FlashErase) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    firmwareupdatefile.o(i.Drv_FlashWrite) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    firmwareupdatefile.o(i.Drv_FlashWrite) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    firmwareupdatefile.o(i.Drv_FlashWrite) refers to firmwareupdatefile.o(i.Drv_FlashErase) for Drv_FlashErase
    firmwareupdatefile.o(i.Drv_FlashWrite) refers to gd32f4xx_fmc.o(i.fmc_word_program) for fmc_word_program
    firmwareupdatefile.o(i.Drv_FlashWrite) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    firmwareupdatefile.o(i.Drv_FlashWrite1) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    firmwareupdatefile.o(i.Drv_FlashWrite1) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    firmwareupdatefile.o(i.Drv_FlashWrite1) refers to firmwareupdatefile.o(i.Drv_FlashErase) for Drv_FlashErase
    firmwareupdatefile.o(i.Drv_FlashWrite1) refers to gd32f4xx_fmc.o(i.fmc_word_program) for fmc_word_program
    firmwareupdatefile.o(i.Drv_FlashWrite1) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    firmwareupdatefile.o(i.Drv_FlashWrite_8bit) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    firmwareupdatefile.o(i.Drv_FlashWrite_8bit) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    firmwareupdatefile.o(i.Drv_FlashWrite_8bit) refers to gd32f4xx_fmc.o(i.fmc_byte_program) for fmc_byte_program
    firmwareupdatefile.o(i.Drv_FlashWrite_8bit) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    firmwareupdatefile.o(i.FlashTest) refers to memseta.o(.text) for __aeabi_memclr4
    firmwareupdatefile.o(i.FlashTest) refers to firmwareupdatefile.o(i.Drv_FlashRead) for Drv_FlashRead
    firmwareupdatefile.o(i.FlashTest) refers to firmwareupdatefile.o(i.Drv_FlashRead_8bit) for Drv_FlashRead_8bit
    firmwareupdatefile.o(i.UartDataHandle) refers to memseta.o(.text) for __aeabi_memclr
    firmwareupdatefile.o(i.UartDataHandle) refers to firmwareupdatefile.o(i.Drv_SystemReset) for Drv_SystemReset
    firmwareupdatefile.o(i.UartDataHandle) refers to memcpya.o(.text) for __aeabi_memcpy
    firmwareupdatefile.o(i.UartDataHandle) refers to firmwareupdatefile.o(i.Uart_UpdateFirm_nav) for Uart_UpdateFirm_nav
    firmwareupdatefile.o(i.UartDataHandle) refers to gd32f4xx_it.o(.bss) for grxbuffer6
    firmwareupdatefile.o(i.UartDataHandle) refers to firmwareupdatefile.o(.data) for error_count
    firmwareupdatefile.o(i.UartDataHandle) refers to gd32f4xx_it.o(.data) for grxlen6
    firmwareupdatefile.o(i.UartDataHandle) refers to firmwareupdatefile.o(.bss) for UpdateFirmWareRx
    firmwareupdatefile.o(i.UartSend_UpdateFirm) refers to memseta.o(.text) for __aeabi_memclr4
    firmwareupdatefile.o(i.UartSend_UpdateFirm) refers to memcpya.o(.text) for __aeabi_memcpy4
    firmwareupdatefile.o(i.UartSend_UpdateFirm) refers to firmwareupdatefile.o(i.UartSend_combag_setheadend_senddo_uart6) for UartSend_combag_setheadend_senddo_uart6
    firmwareupdatefile.o(i.UartSend_UpdateFirm) refers to firmwareupdatefile.o(.bss) for UartTxBuf
    firmwareupdatefile.o(i.UartSend_combag_setheadend_senddo_uart6) refers to firmwareupdatefile.o(i.UartSend_nav2up_SetHead) for UartSend_nav2up_SetHead
    firmwareupdatefile.o(i.UartSend_combag_setheadend_senddo_uart6) refers to firmwareupdatefile.o(i.UartSend_nav2up_SetEnd) for UartSend_nav2up_SetEnd
    firmwareupdatefile.o(i.UartSend_combag_setheadend_senddo_uart6) refers to gd32f4xx_it.o(i.UpdateFirmsendmsg) for UpdateFirmsendmsg
    firmwareupdatefile.o(i.UartSend_combag_setheadend_senddo_uart6) refers to firmwareupdatefile.o(.bss) for UartTxBuf
    firmwareupdatefile.o(i.UartSend_nav2up_SetEnd) refers to firmwareupdatefile.o(i.ushort2Buf) for ushort2Buf
    firmwareupdatefile.o(i.UartSend_nav2up_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    firmwareupdatefile.o(i.UartSend_nav2up_SetEnd) refers to firmwareupdatefile.o(.data) for SendBagCount_nav
    firmwareupdatefile.o(i.UartSend_nav2up_SetEnd) refers to firmwareupdatefile.o(.bss) for UartTxBuf
    firmwareupdatefile.o(i.UartSend_nav2up_SetHead) refers to firmwareupdatefile.o(i.ushort2Buf) for ushort2Buf
    firmwareupdatefile.o(i.UartSend_nav2up_SetHead) refers to firmwareupdatefile.o(.bss) for UartTxBuf
    firmwareupdatefile.o(i.UartWrite) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    firmwareupdatefile.o(i.UartWrite) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    firmwareupdatefile.o(i.Uart_UpdateFirm_nav) refers to memcpya.o(.text) for __aeabi_memcpy4
    firmwareupdatefile.o(i.Uart_UpdateFirm_nav) refers to firmwareupdatefile.o(i.CheckSum) for CheckSum
    firmwareupdatefile.o(i.Uart_UpdateFirm_nav) refers to firmwareupdatefile.o(i.UpdateFileHandle) for UpdateFileHandle
    firmwareupdatefile.o(i.Uart_UpdateFirm_nav) refers to firmwareupdatefile.o(i.UartSend_UpdateFirm) for UartSend_UpdateFirm
    firmwareupdatefile.o(i.Uart_UpdateFirm_nav) refers to firmwareupdatefile.o(i.Drv_SystemReset) for Drv_SystemReset
    firmwareupdatefile.o(i.Uart_UpdateFirm_nav) refers to firmwareupdatefile.o(.bss) for UpdateFirmWareRx
    firmwareupdatefile.o(i.Uart_UpdateFirm_nav) refers to firmwareupdatefile.o(.data) for g_StartUpdateFirm
    firmwareupdatefile.o(i.UpdateFileHandle) refers to firmwareupdatefile.o(i.Drv_FlashErase) for Drv_FlashErase
    firmwareupdatefile.o(i.UpdateFileHandle) refers to firmwareupdatefile.o(i.Drv_FlashWrite) for Drv_FlashWrite
    firmwareupdatefile.o(i.UpdateFileHandle) refers to firmwareupdatefile.o(.data) for uiLastBaoInDex
    firmwareupdatefile.o(i.UpdateFileHandle) refers to firmwareupdatefile.o(.bss) for UpdateFirmWareTx
    firmwareupdatefile.o(i.ushort2Buf) refers to firmwareupdatefile.o(i.bufExchangeHL2pos) for bufExchangeHL2pos
    bmp2.o(i.bmp2_compensate_data) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_compensate_data) refers to bmp2.o(i.compensate_temperature) for compensate_temperature
    bmp2.o(i.bmp2_compensate_data) refers to bmp2.o(i.compensate_pressure) for compensate_pressure
    bmp2.o(i.bmp2_compute_meas_time) refers to memcpya.o(.text) for __aeabi_memcpy4
    bmp2.o(i.bmp2_compute_meas_time) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_compute_meas_time) refers to bmp2.o(.constdata) for .constdata
    bmp2.o(i.bmp2_get_config) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_get_power_mode) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_get_regs) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_get_sensor_data) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_get_sensor_data) refers to bmp2.o(i.parse_sensor_data) for parse_sensor_data
    bmp2.o(i.bmp2_get_sensor_data) refers to bmp2.o(i.bmp2_compensate_data) for bmp2_compensate_data
    bmp2.o(i.bmp2_get_status) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_init) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_init) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_init) refers to bmp2.o(i.get_calib_param) for get_calib_param
    bmp2.o(i.bmp2_set_config) refers to bmp2.o(i.conf_sensor) for conf_sensor
    bmp2.o(i.bmp2_set_power_mode) refers to bmp2.o(i.conf_sensor) for conf_sensor
    bmp2.o(i.bmp2_set_regs) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_set_regs) refers to bmp2.o(i.interleave_data) for interleave_data
    bmp2.o(i.bmp2_soft_reset) refers to bmp2.o(i.bmp2_set_regs) for bmp2_set_regs
    bmp2.o(i.compensate_pressure) refers to dflti.o(.text) for __aeabi_i2d
    bmp2.o(i.compensate_pressure) refers to ddiv.o(.text) for __aeabi_ddiv
    bmp2.o(i.compensate_pressure) refers to dadd.o(.text) for __aeabi_dsub
    bmp2.o(i.compensate_pressure) refers to dmul.o(.text) for __aeabi_dmul
    bmp2.o(i.compensate_pressure) refers to dfltui.o(.text) for __aeabi_ui2d
    bmp2.o(i.compensate_pressure) refers to cdcmple.o(.text) for __aeabi_cdcmple
    bmp2.o(i.compensate_pressure) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    bmp2.o(i.compensate_temperature) refers to dflti.o(.text) for __aeabi_i2d
    bmp2.o(i.compensate_temperature) refers to dfltui.o(.text) for __aeabi_ui2d
    bmp2.o(i.compensate_temperature) refers to ddiv.o(.text) for __aeabi_ddiv
    bmp2.o(i.compensate_temperature) refers to dadd.o(.text) for __aeabi_dsub
    bmp2.o(i.compensate_temperature) refers to dmul.o(.text) for __aeabi_dmul
    bmp2.o(i.compensate_temperature) refers to dfixi.o(.text) for __aeabi_d2iz
    bmp2.o(i.compensate_temperature) refers to cdcmple.o(.text) for __aeabi_cdcmple
    bmp2.o(i.compensate_temperature) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.bmp2_soft_reset) for bmp2_soft_reset
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.set_os_mode) for set_os_mode
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.bmp2_set_regs) for bmp2_set_regs
    bmp2.o(i.get_calib_param) refers to memseta.o(.text) for __aeabi_memclr4
    bmp2.o(i.get_calib_param) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.parse_sensor_data) refers to bmp2.o(i.st_check_boundaries) for st_check_boundaries
    bmp280.o(i.bmp280_get_data) refers to bmp2.o(i.bmp2_get_status) for bmp2_get_status
    bmp280.o(i.bmp280_get_data) refers to bmp2.o(i.bmp2_get_sensor_data) for bmp2_get_sensor_data
    bmp280.o(i.bmp280_init) refers to common.o(i.bmp2_interface_selection) for bmp2_interface_selection
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_init) for bmp2_init
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_get_config) for bmp2_get_config
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_set_config) for bmp2_set_config
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_set_power_mode) for bmp2_set_power_mode
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_compute_meas_time) for bmp2_compute_meas_time
    bmp280.o(i.bmp280_init) refers to bmp280.o(.bss) for bmpDev
    bmp280.o(i.bmp280_init) refers to bmp280.o(.data) for bmpCfg
    bsp_adc.o(i.adc_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_special_function_config) for adc_special_function_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_regular_channel_config) for adc_regular_channel_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_watchdog_threshold_config) for adc_watchdog_threshold_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable) for adc_watchdog_single_channel_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_watchdog_group_channel_enable) for adc_watchdog_group_channel_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_interrupt_enable) for adc_interrupt_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    bsp_adc.o(i.adc_config) refers to systick.o(i.delay_ms) for delay_ms
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    bsp_adc.o(i.adc_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    bsp_adc.o(i.enter_sleep_mode) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_adc.o(i.enter_sleep_mode) refers to gd32f4xx_rcu.o(i.rcu_bkp_reset_enable) for rcu_bkp_reset_enable
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to gd32f4xx_can.o(i.can_message_receive) for can_message_receive
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to bsp_can.o(.data) for hCAN0
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to main.o(.data) for gcan0_rx_syn
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to bsp_can.o(.bss) for gCanRxBuf
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to ins_data.o(.bss) for hINSData
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to main.o(.bss) for combineData
    bsp_can.o(i.CAN1_RX0_IRQHandler) refers to gd32f4xx_can.o(i.can_message_receive) for can_message_receive
    bsp_can.o(i.CAN1_RX0_IRQHandler) refers to bsp_can.o(.data) for hCAN1
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_deinit) for can_deinit
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_init) for can_init
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_filter_init) for can_filter_init
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_interrupt_enable) for can_interrupt_enable
    bsp_can.o(i.bsp_can_transmit) refers to gd32f4xx_can.o(i.can_struct_para_init) for can_struct_para_init
    bsp_can.o(i.bsp_can_transmit) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_can.o(i.bsp_can_transmit) refers to gd32f4xx_can.o(i.can_message_transmit) for can_message_transmit
    bsp_can.o(i.can_transmit_acc) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_acc) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_angle) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_angle) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_gyro) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_gyro) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_h) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_h) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_pos) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_pos) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_speed) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_speed) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_status) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_status) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_std_heading) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_std_heading) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_temperature) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_temperature) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.range) refers to cdcmple.o(.text) for __aeabi_cdcmple
    bsp_can.o(i.range) refers to dadd.o(.text) for __aeabi_dsub
    bsp_can.o(i.stdDev) refers to dadd.o(.text) for __aeabi_dadd
    bsp_can.o(i.stdDev) refers to dfltui.o(.text) for __aeabi_ui2d
    bsp_can.o(i.stdDev) refers to ddiv.o(.text) for __aeabi_ddiv
    bsp_can.o(i.stdDev) refers to dmul.o(.text) for __aeabi_dmul
    bsp_can.o(i.stdDev) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    bsp_can.o(i.uart4sendmsg_canout) refers to printfa.o(i.__0sprintf) for __2sprintf
    bsp_can.o(i.uart4sendmsg_canout) refers to strlen.o(.text) for strlen
    bsp_can.o(i.uart4sendmsg_canout) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    bsp_can.o(i.uart4sendmsg_canout) refers to main.o(.data) for gcan0_rx_syn
    bsp_can.o(i.uart4sendmsg_canout) refers to bsp_can.o(.conststring) for .conststring
    bsp_can.o(i.variance) refers to dadd.o(.text) for __aeabi_dadd
    bsp_can.o(i.variance) refers to dfltui.o(.text) for __aeabi_ui2d
    bsp_can.o(i.variance) refers to ddiv.o(.text) for __aeabi_ddiv
    bsp_can.o(i.variance) refers to dmul.o(.text) for __aeabi_dmul
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_syscfg.o(i.syscfg_exti_line_config) for syscfg_exti_line_config
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_exti.o(i.exti_init) for exti_init
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    bsp_exti.o(i.bsp_exti_init) refers to bsp_exti.o(i.bsp_exti_config) for bsp_exti_config
    bsp_exti.o(i.bsp_exti_init) refers to bsp_exti.o(.data) for hEXTI
    bsp_flash.o(i.EndWrite) refers to bsp_flash.o(i.WriteOld) for WriteOld
    bsp_flash.o(i.EndWrite) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_flash.o(i.EndWrite) refers to bsp_flash.o(.data) for g_NeedWrite
    bsp_flash.o(i.InitFlashAddr) refers to bsp_flash.o(.data) for g_Addr
    bsp_flash.o(i.ReadFlash) refers to bsp_flash.o(.data) for g_Addr
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(i.WriteOld) for WriteOld
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(i.ReadFlashByAddr) for ReadFlashByAddr
    bsp_flash.o(i.WriteFlash) refers to memcpya.o(.text) for __aeabi_memcpy
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_halfword_program) for fmc_halfword_program
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(.data) for g_Addr
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(.bss) for g_FlashBuf
    bsp_flash.o(i.WriteOld) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_flash.o(i.WriteOld) refers to gd32f4xx_fmc.o(i.fmc_halfword_program) for fmc_halfword_program
    bsp_flash.o(i.WriteOld) refers to bsp_flash.o(.bss) for g_FlashBuf
    bsp_flash.o(i.WriteOld) refers to bsp_flash.o(.data) for g_AddrBase
    bsp_fmc.o(i.Uart_SendMsg) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_exmc.o(i.exmc_norsram_init) for exmc_norsram_init
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_exmc.o(i.exmc_norsram_enable) for exmc_norsram_enable
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_sdram_init) for exmc_sdram_init
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_flag_get) for exmc_flag_get
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_sdram_command_config) for exmc_sdram_command_config
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to systick.o(i.delay_ms) for delay_ms
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set) for exmc_sdram_refresh_count_set
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_sector_info_get) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_16bit_data) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_16bit_data) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_16bit_data) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_halfword_program) for fmc_halfword_program
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_32bit_data) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_32bit_data) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_32bit_data) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_word_program) for fmc_word_program
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_8bit_data) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_8bit_data) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_8bit_data) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_byte_program) for fmc_byte_program
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to gd32f4xx_fmc.o(i.fmc_byte_program) for fmc_byte_program
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fwdgt.o(i.bsp_fwdgt_feed) refers to gd32f4xx_fwdgt.o(i.fwdgt_counter_reload) for fwdgt_counter_reload
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_fwdgt.o(i.fwdgt_config) for fwdgt_config
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_fwdgt.o(i.fwdgt_enable) for fwdgt_enable
    bsp_gpio.o(i.bsp_gpio_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_gpio.o(i.bsp_gpio_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_gpio.o(i.bsp_gpio_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_gpio.o(i.bsp_gpio_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to gd32f4xx_rtc.o(i.rtc_flag_get) for rtc_flag_get
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to bsp_rtc.o(i.rtc_show_timestamp) for rtc_show_timestamp
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to gd32f4xx_rtc.o(i.rtc_flag_clear) for rtc_flag_clear
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to bsp_rtc.o(.data) for pTimestamep
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to bsp_rtc.o(i.rtc_pre_config) for rtc_pre_config
    bsp_rtc.o(i.bsp_rtc_init) refers to bsp_rtc.o(i.rtc_setup) for rtc_setup
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rtc.o(i.rtc_timestamp_enable) for rtc_timestamp_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rtc.o(i.rtc_interrupt_enable) for rtc_interrupt_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rtc.o(i.rtc_flag_clear) for rtc_flag_clear
    bsp_rtc.o(i.getRTCWeekSecond) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    bsp_rtc.o(i.getRTCWeekSecond) refers to bsp_rtc.o(i.yearDay) for yearDay
    bsp_rtc.o(i.getRTCWeekSecond) refers to mktime.o(.text) for mktime
    bsp_rtc.o(i.getRTCWeekSecond) refers to gd32f4xx_rtc.o(i.rtc_subsecond_get) for rtc_subsecond_get
    bsp_rtc.o(i.getRTCWeekSecond) refers to f2d.o(.text) for __aeabi_f2d
    bsp_rtc.o(i.getRTCWeekSecond) refers to time_unify.o(i.time2gpst) for time2gpst
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    bsp_rtc.o(i.rtc_setup) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    bsp_rtc.o(i.rtc_show_timestamp) refers to memseta.o(.text) for __aeabi_memclr
    bsp_rtc.o(i.rtc_show_timestamp) refers to gd32f4xx_rtc.o(i.rtc_timestamp_get) for rtc_timestamp_get
    bsp_rtc.o(i.rtc_show_timestamp) refers to gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get) for rtc_timestamp_subsecond_get
    bsp_rtc.o(i.rtc_show_timestamp) refers to printfa.o(i.__0sprintf) for __2sprintf
    bsp_rtc.o(i.timeSync) refers to time_unify.o(i.gpst2time) for gpst2time
    bsp_rtc.o(i.timeSync) refers to time_unify.o(i.gpst2utc) for gpst2utc
    bsp_rtc.o(i.timeSync) refers to localtime_w.o(.text) for localtime
    bsp_rtc.o(i.timeSync) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_rtc.o(i.timeSync) refers to bsp_rtc.o(i.rtc_setup) for rtc_setup
    bsp_rtc.o(i.yearDay) refers to bsp_rtc.o(i.isLeapYear) for isLeapYear
    bsp_rtc.o(.data) refers to bsp_rtc.o(.bss) for gTimeStamp
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config) for rcu_timer_clock_prescaler_config
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_channel_output_config) for timer_channel_output_config
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config) for timer_channel_output_pulse_value_config
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_channel_output_mode_config) for timer_channel_output_mode_config
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_channel_output_shadow_config) for timer_channel_output_shadow_config
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable) for timer_auto_reload_shadow_enable
    bsp_uart.o(i.USART4_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    bsp_uart.o(i.USART4_IRQHandler) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    bsp_uart.o(i.USART4_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    bsp_uart.o(i.USART4_IRQHandler) refers to gd32f4xx_it.o(.data) for grxlen
    bsp_uart.o(i.USART4_IRQHandler) refers to gd32f4xx_it.o(.bss) for grxbuffer
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    ch378_hal.o(i.CH378_mDelaymS) refers to systick.o(i.delay_ms) for delay_ms
    ch378_hal.o(i.CH378_mDelayuS) refers to systick.o(i.delay_us) for delay_us
    ch378_hal.o(i.mStopIfError) refers to printfa.o(i.__0printf) for __2printf
    ch395cmd.o(i.CH395CMDCheckExist) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDCheckExist) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDCheckExist) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDCheckExist) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetGlobIntStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetGlobIntStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395CMDGetGlobIntStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetGlobIntStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetGlobIntStatus_ALL) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetGlobIntStatus_ALL) refers to ch395spi.o(i.mDelayuS) for mDelayuS
    ch395cmd.o(i.CH395CMDGetGlobIntStatus_ALL) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395CMDGetGlobIntStatus_ALL) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetGlobIntStatus_ALL) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetMACAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetMACAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395CMDGetMACAddr) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetMACAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetPHYStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetPHYStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395CMDGetPHYStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetPHYStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetRemoteIPP) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetRemoteIPP) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDGetRemoteIPP) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetRemoteIPP) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetSocketStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetSocketStatus) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDGetSocketStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetSocketStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetUnreachIPPT) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetUnreachIPPT) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395CMDGetUnreachIPPT) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetUnreachIPPT) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetVer) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetVer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395CMDGetVer) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetVer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDInitCH395) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDInitCH395) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDInitCH395) refers to systick.o(i.delay_ms) for delay_ms
    ch395cmd.o(i.CH395CMDInitCH395) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395CMDInitCH395) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395CMDReset) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDReset) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetGWIPAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetGWIPAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetGWIPAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetIPAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetIPAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetIPAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395CMDSetMACFilt) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetMACFilt) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetMACFilt) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetMASKAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetMASKAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetMASKAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetPHY) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetPHY) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetPHY) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetRetryCount) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetRetryCount) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetRetryCount) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetRetryPeriod) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetRetryPeriod) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetRetryPeriod) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetUartBaudRate) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetUartBaudRate) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetUartBaudRate) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSleep) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSleep) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395ClearRecvBuf) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395ClearRecvBuf) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395ClearRecvBuf) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CloseSocket) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CloseSocket) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CloseSocket) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CloseSocket) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395CloseSocket) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395DHCPEnable) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395DHCPEnable) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395DHCPEnable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395DHCPEnable) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395DHCPEnable) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395EEPROMErase) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395EEPROMErase) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395EEPROMErase) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395EEPROMRead) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395EEPROMRead) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395EEPROMRead) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395EEPROMRead) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395EEPROMRead) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395EEPROMWrite) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395EEPROMWrite) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395EEPROMWrite) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395EEPROMWrite) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395EnablePing) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395EnablePing) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395EnablePing) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395GetCmdStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetCmdStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395GetCmdStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395GetCmdStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetDHCPStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetDHCPStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395GetDHCPStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetDHCPStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395GetIPInf) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetIPInf) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395GetIPInf) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetIPInf) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395GetRecvData) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetRecvData) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395GetRecvData) refers to ch395spi.o(i.mDelayuS) for mDelayuS
    ch395cmd.o(i.CH395GetRecvData) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetRecvData) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395GetRecvLength) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetRecvLength) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395GetRecvLength) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetRecvLength) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395GetSocketInt) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetSocketInt) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395GetSocketInt) refers to ch395spi.o(i.mDelayuS) for mDelayuS
    ch395cmd.o(i.CH395GetSocketInt) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetSocketInt) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395OpenSocket) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395OpenSocket) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395OpenSocket) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395OpenSocket) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395OpenSocket) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395ReadGPIOAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395ReadGPIOAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395ReadGPIOAddr) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395ReadGPIOAddr) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395SendData) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SendData) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SendData) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395SetSocketDesIP) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketDesIP) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketDesIP) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395SetSocketDesPort) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketDesPort) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketDesPort) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395SetSocketIPRAWProto) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketIPRAWProto) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketIPRAWProto) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395SetSocketProtType) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketProtType) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketProtType) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395SetSocketRecvBuf) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketRecvBuf) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketRecvBuf) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395SetSocketSendBuf) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketSendBuf) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketSendBuf) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395SetSocketSourPort) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketSourPort) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketSourPort) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395SetStartPara) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetStartPara) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetStartPara) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395SetTCPMss) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetTCPMss) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetTCPMss) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395TCPConnect) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395TCPConnect) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395TCPConnect) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395TCPConnect) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395TCPConnect) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395TCPDisconnect) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395TCPDisconnect) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395TCPDisconnect) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395TCPDisconnect) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395TCPDisconnect) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395TCPListen) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395TCPListen) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395TCPListen) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395TCPListen) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395TCPListen) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395UDPSendTo) refers to ch395cmd.o(i.CH395SetSocketDesIP) for CH395SetSocketDesIP
    ch395cmd.o(i.CH395UDPSendTo) refers to ch395cmd.o(i.CH395SetSocketDesPort) for CH395SetSocketDesPort
    ch395cmd.o(i.CH395UDPSendTo) refers to ch395cmd.o(i.CH395SendData) for CH395SendData
    ch395cmd.o(i.CH395WriteGPIOAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395WriteGPIOAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_spi.o(i.spi_crc_off) for spi_crc_off
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    ch395spi.o(i.CH395_RST) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395spi.o(i.CH395_RST) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395spi.o(i.CH395_RST) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395spi.o(i.Query395Interrupt) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    ch395spi.o(i.Spi395Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    ch395spi.o(i.Spi395Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    ch395spi.o(i.Spi395Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    ch395spi.o(i.mDelaymS) refers to systick.o(i.delay_ms) for delay_ms
    ch395spi.o(i.mDelayuS) refers to systick.o(i.delay_us) for delay_us
    ch395spi.o(i.xReadCH395Data) refers to ch395spi.o(i.Spi395Exchange) for Spi395Exchange
    ch395spi.o(i.xWriteCH395Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395spi.o(i.xWriteCH395Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395spi.o(i.xWriteCH395Cmd) refers to ch395spi.o(i.Spi395Exchange) for Spi395Exchange
    ch395spi.o(i.xWriteCH395Cmd) refers to ch395spi.o(i.mDelayuS) for mDelayuS
    ch395spi.o(i.xWriteCH395Data) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395spi.o(i.xWriteCH395Data) refers to ch395spi.o(i.Spi395Exchange) for Spi395Exchange
    common.o(i.bmp2_delay_us) refers to systick.o(i.delay_us) for delay_us
    common.o(i.bmp2_i2c_read) refers to common.o(i.i2c_read) for i2c_read
    common.o(i.bmp2_i2c_read) refers to common.o(.data) for dev_addr
    common.o(i.bmp2_i2c_write) refers to common.o(i.i2c_write) for i2c_write
    common.o(i.bmp2_i2c_write) refers to common.o(.data) for dev_addr
    common.o(i.bmp2_interface_selection) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init) for Soft_I2C_Master_Init
    common.o(i.bmp2_interface_selection) refers to systick.o(i.delay_ms) for delay_ms
    common.o(i.bmp2_interface_selection) refers to common.o(.data) for dev_addr
    common.o(i.bmp2_interface_selection) refers to common.o(i.bmp2_i2c_read) for bmp2_i2c_read
    common.o(i.bmp2_interface_selection) refers to common.o(i.bmp2_i2c_write) for bmp2_i2c_write
    common.o(i.bmp2_interface_selection) refers to common.o(.bss) for hSoftI2c
    common.o(i.bmp2_interface_selection) refers to common.o(i.bmp2_delay_us) for bmp2_delay_us
    common.o(i.i2c_read) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Start) for Soft_I2C_Start
    common.o(i.i2c_read) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) for Soft_I2C_Send_Byte
    common.o(i.i2c_read) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) for Soft_I2C_Wait_Ack
    common.o(i.i2c_read) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) for Soft_I2C_Read_Byte
    common.o(i.i2c_read) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    common.o(i.i2c_read) refers to common.o(.bss) for hSoftI2c
    common.o(i.i2c_write) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Start) for Soft_I2C_Start
    common.o(i.i2c_write) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) for Soft_I2C_Send_Byte
    common.o(i.i2c_write) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) for Soft_I2C_Wait_Ack
    common.o(i.i2c_write) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    common.o(i.i2c_write) refers to common.o(.bss) for hSoftI2c
    file_sys.o(i.CH378AutoEnumDevice) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378AutoInitDisk) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378AutoResetDisk) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378BlockOnlyCMD) refers to file_sys.o(i.CH378WriteOfsBlock) for CH378WriteOfsBlock
    file_sys.o(i.CH378BlockOnlyCMD) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378BlockOnlyCMD) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378ByteLocate) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ByteLocate) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ByteLocate) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ByteLocate) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378ByteRead) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ByteRead) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ByteRead) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ByteRead) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378ByteRead) refers to file_sys.o(i.CH378GetTrueLen) for CH378GetTrueLen
    file_sys.o(i.CH378ByteRead) refers to file_sys.o(i.CH378ReadBlock) for CH378ReadBlock
    file_sys.o(i.CH378ByteReadPrepare) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ByteReadPrepare) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ByteReadPrepare) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ByteReadPrepare) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378ByteReadPrepare) refers to file_sys.o(i.CH378GetTrueLen) for CH378GetTrueLen
    file_sys.o(i.CH378ByteWrite) refers to file_sys.o(i.CH378WriteOfsBlock) for CH378WriteOfsBlock
    file_sys.o(i.CH378ByteWrite) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ByteWrite) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ByteWrite) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ByteWrite) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378ByteWriteExecute) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ByteWriteExecute) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ByteWriteExecute) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ByteWriteExecute) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378CheckExist) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378CheckExist) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378CheckExist) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378CheckExist) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ClearStall) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    file_sys.o(i.CH378DirCreate) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    file_sys.o(i.CH378DirCreate) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DirInfoRead) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    file_sys.o(i.CH378DirInfoSave) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    file_sys.o(i.CH378DiskCapacity) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DiskCapacity) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378DiskCapacity) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    file_sys.o(i.CH378DiskCapacity) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378DiskCapacity) refers to file_sys.o(i.CH378Read32bitDat) for CH378Read32bitDat
    file_sys.o(i.CH378DiskConnect) refers to ch378_spi_hw.o(i.Query378Interrupt) for Query378Interrupt
    file_sys.o(i.CH378DiskConnect) refers to file_sys.o(i.CH378GetIntStatus) for CH378GetIntStatus
    file_sys.o(i.CH378DiskConnect) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DiskQuery) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DiskQuery) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378DiskQuery) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    file_sys.o(i.CH378DiskQuery) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378DiskQuery) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378DiskReadSec) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378DiskReadSec) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378DiskReadSec) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378DiskReadSec) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378DiskReadSec) refers to file_sys.o(i.CH378ReadBlock) for CH378ReadBlock
    file_sys.o(i.CH378DiskReadSec) refers to file_sys.o(.data) for SectorSize
    file_sys.o(i.CH378DiskReady) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DiskWriteSec) refers to file_sys.o(i.CH378WriteOfsBlock) for CH378WriteOfsBlock
    file_sys.o(i.CH378DiskWriteSec) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378DiskWriteSec) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378DiskWriteSec) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378DiskWriteSec) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378DiskWriteSec) refers to file_sys.o(.data) for SectorSize
    file_sys.o(i.CH378EnterFullSleep) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378EnterFullSleep) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378EnterFullSleep) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378EnterFullSleep) refers to ch378_hal.o(i.CH378_mDelaymS) for CH378_mDelaymS
    file_sys.o(i.CH378EnterHalfSleep) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378EnterHalfSleep) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378EnterHalfSleep) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378EnterHalfSleep) refers to ch378_hal.o(i.CH378_mDelaymS) for CH378_mDelaymS
    file_sys.o(i.CH378FileClose) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    file_sys.o(i.CH378FileCreate) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    file_sys.o(i.CH378FileCreate) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378FileErase) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    file_sys.o(i.CH378FileErase) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378FileModify) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378FileModify) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378FileModify) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378FileModify) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378FileOpen) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    file_sys.o(i.CH378FileOpen) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378FileQuery) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378FileQuery) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378GetDiskInquiry) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378GetDiskInquiry) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378GetDiskReady) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378GetDiskReqSense) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378GetDiskReqSense) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378GetDiskSize) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378GetDiskSize) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378GetDiskStatus) refers to file_sys.o(i.CH378ReadVar8) for CH378ReadVar8
    file_sys.o(i.CH378GetFileSize) refers to file_sys.o(i.CH378ReadVar32) for CH378ReadVar32
    file_sys.o(i.CH378GetICVer) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetICVer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    file_sys.o(i.CH378GetICVer) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378GetICVer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378GetIntStatus) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetIntStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    file_sys.o(i.CH378GetIntStatus) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378GetIntStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378GetTrueLen) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetTrueLen) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378GetTrueLen) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378HardwareReset) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378HardwareReset) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378HardwareReset) refers to ch378_hal.o(i.CH378_mDelaymS) for CH378_mDelaymS
    file_sys.o(i.CH378Read32bitDat) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    file_sys.o(i.CH378Read32bitDat) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378Read32bitDat) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ReadBlock) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadBlock) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadBlock) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378ReadBlock) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378ReadBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ReadOfsBlock) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadOfsBlock) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadOfsBlock) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378ReadOfsBlock) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378ReadOfsBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ReadReqBlock) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadReqBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    file_sys.o(i.CH378ReadReqBlock) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378ReadReqBlock) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378ReadReqBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ReadVar32) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadVar32) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadVar32) refers to file_sys.o(i.CH378Read32bitDat) for CH378Read32bitDat
    file_sys.o(i.CH378ReadVar32) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ReadVar8) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadVar8) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadVar8) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378ReadVar8) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SecLocate) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SecLocate) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SecLocate) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SecLocate) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SecRead) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SecRead) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SecRead) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SecRead) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SecRead) refers to file_sys.o(i.CH378GetTrueLen) for CH378GetTrueLen
    file_sys.o(i.CH378SecRead) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378SecRead) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378SecRead) refers to file_sys.o(.data) for SectorSize
    file_sys.o(i.CH378SecWrite) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SecWrite) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SecWrite) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SecWrite) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SecWrite) refers to file_sys.o(.data) for SectorSize
    file_sys.o(i.CH378SelfEnumDevice) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    file_sys.o(i.CH378SelfEnumDevice) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SendCmdWaitInt) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SendCmdWaitInt) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SendCmdWaitInt) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SetFileName) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SetFileName) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SetFileName) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SetFileSize) refers to file_sys.o(i.CH378WriteVar32) for CH378WriteVar32
    file_sys.o(i.CH378WriteBlock) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteBlock) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteBlock) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378WriteBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378WriteOfsBlock) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteOfsBlock) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteOfsBlock) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378WriteOfsBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378WriteVar32) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteVar32) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteVar32) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378WriteVar8) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteVar8) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteVar8) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.Wait378Interrupt) refers to ch378_spi_hw.o(i.Query378Interrupt) for Query378Interrupt
    file_sys.o(i.Wait378Interrupt) refers to file_sys.o(i.CH378GetIntStatus) for CH378GetIntStatus
    file_sys.o(i.Wait378Interrupt) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    logger.o(i.CopyAndConvertFile) refers to printfa.o(i.__0printf) for __2printf
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378FileOpen) for CH378FileOpen
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378GetFileSize) for CH378GetFileSize
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378ByteLocate) for CH378ByteLocate
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378ByteRead) for CH378ByteRead
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378FileCreate) for CH378FileCreate
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378ByteWrite) for CH378ByteWrite
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378FileClose) for CH378FileClose
    logger.o(i.CopyAndConvertFile) refers to logger.o(.bss) for fileBuf
    logger.o(i.generateCSVLogFileName) refers to memseta.o(.text) for __aeabi_memclr4
    logger.o(i.generateCSVLogFileName) refers to time_unify.o(i.sow2Date) for sow2Date
    logger.o(i.generateCSVLogFileName) refers to printfa.o(i.__0sprintf) for __2sprintf
    logger.o(i.generateCSVLogFileName) refers to strcpy.o(.text) for strcpy
    logger.o(i.generateCSVLogFileName) refers to main.o(.data) for g_gpsSecond
    logger.o(i.parseFPGABuff) refers to dflti.o(.text) for __aeabi_i2d
    logger.o(i.parseFPGABuff) refers to dmul.o(.text) for __aeabi_dmul
    logger.o(i.parseFPGABuff) refers to d2f.o(.text) for __aeabi_d2f
    logger.o(i.parseFPGABuff) refers to ddiv.o(.text) for __aeabi_ddiv
    logger.o(i.parseFPGABuff) refers to f2d.o(.text) for __aeabi_f2d
    logger.o(i.parseFPGABuff) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    logger.o(i.parseFPGABuff) refers to ins_data.o(.data) for fpga_data_read_flag
    logger.o(i.parseFPGABuff) refers to ins_data.o(.bss) for hINSFPGAData
    logger.o(i.synthesisLogBuf) refers to memseta.o(.text) for __aeabi_memclr4
    logger.o(i.synthesisLogBuf) refers to time_unify.o(i.gpst2time) for gpst2time
    logger.o(i.synthesisLogBuf) refers to time_unify.o(i.gpst2utc) for gpst2utc
    logger.o(i.synthesisLogBuf) refers to logger.o(i.parseFPGABuff) for parseFPGABuff
    logger.o(i.synthesisLogBuf) refers to time_unify.o(i.time2str) for time2str
    logger.o(i.synthesisLogBuf) refers to f2d.o(.text) for __aeabi_f2d
    logger.o(i.synthesisLogBuf) refers to printfa.o(i.__0sprintf) for __2sprintf
    logger.o(i.synthesisLogBuf) refers to strlen.o(.text) for strlen
    logger.o(i.synthesisLogBuf) refers to logger.o(.bss) for g_charBuf
    logger.o(i.synthesisLogBuf) refers to logger.o(.conststring) for .conststring
    logger.o(i.writeCSVFileHead) refers to memseta.o(.text) for __aeabi_memclr
    logger.o(i.writeCSVFileHead) refers to strcpy.o(.text) for strcpy
    logger.o(i.writeCSVFileHead) refers to strlen.o(.text) for strlen
    logger.o(i.writeCSVFileHead) refers to file_sys.o(i.CH378ByteWrite) for CH378ByteWrite
    logger.o(i.writeCSVFileHead) refers to logger.o(.bss) for g_headCharBuf
    logger.o(i.writeCSVFileHead) refers to logger.o(.conststring) for .conststring
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378GetDiskStatus) for CH378GetDiskStatus
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378FileOpen) for CH378FileOpen
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378FileCreate) for CH378FileCreate
    logger.o(i.writeCSVLog) refers to ch378_hal.o(i.CH378_mDelaymS) for CH378_mDelaymS
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378ByteLocate) for CH378ByteLocate
    logger.o(i.writeCSVLog) refers to printfa.o(i.__0printf) for __2printf
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378ByteWrite) for CH378ByteWrite
    logger.o(i.writeCSVLog) refers to logger.o(i.writeCSVFileHead) for writeCSVFileHead
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_spi.o(i.spi_crc_off) for spi_crc_off
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_spi_hw.o(i.Query378Interrupt) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    ch378_spi_hw.o(i.SPI_Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    ch378_spi_hw.o(i.SPI_Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    ch378_spi_hw.o(i.SPI_Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    ch378_spi_hw.o(i.SPI_Receive) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    ch378_spi_hw.o(i.SPI_Receive) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    ch378_spi_hw.o(i.SPI_Transmit) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    ch378_spi_hw.o(i.SPI_Transmit) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.CH378_Port_Init) for CH378_Port_Init
    ch378_spi_hw.o(i.mInitCH378Host) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_spi_hw.o(i.mInitCH378Host) refers to systick.o(i.delay_us) for delay_us
    ch378_spi_hw.o(i.mInitCH378Host) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_spi_hw.o(i.mInitCH378Host) refers to systick.o(i.delay_ms) for delay_ms
    ch378_spi_hw.o(i.mInitCH378Host) refers to file_sys.o(i.CH378GetICVer) for CH378GetICVer
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    ch378_spi_hw.o(i.xReadCH378Data) refers to ch378_spi_hw.o(i.SPI_Exchange) for SPI_Exchange
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to systick.o(i.delay_us) for delay_us
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to ch378_spi_hw.o(i.SPI_Exchange) for SPI_Exchange
    ch378_spi_hw.o(i.xWriteCH378Data) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_spi_hw.o(i.xWriteCH378Data) refers to ch378_spi_hw.o(i.SPI_Exchange) for SPI_Exchange
    bsp_soft_i2c_master.o(i.Soft_I2C_Ack) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_Ack) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) for Soft_I2C_SDA_Output
    bsp_soft_i2c_master.o(i.Soft_I2C_Ack) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Ack) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_NAck) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_NAck) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) for Soft_I2C_SDA_Output
    bsp_soft_i2c_master.o(i.Soft_I2C_NAck) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_NAck) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Input) for Soft_I2C_SDA_Input
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to bsp_soft_i2c_master.o(i.Soft_I2C_NAck) for Soft_I2C_NAck
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Ack) for Soft_I2C_Ack
    bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Input) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Input) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) for Soft_I2C_SDA_Output
    bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Start) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) for Soft_I2C_SDA_Output
    bsp_soft_i2c_master.o(i.Soft_I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Start) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_Stop) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) for Soft_I2C_SDA_Output
    bsp_soft_i2c_master.o(i.Soft_I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_Stop) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Input) for Soft_I2C_SDA_Input
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) for TAMPER_STAMP_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.ADC_IRQHandler) for ADC_IRQHandler
    startup_gd32f450_470.o(RESET) refers to bsp_can.o(i.CAN0_RX0_IRQHandler) for CAN0_RX0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI5_9_IRQHandler) for EXTI5_9_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) for TIMER0_UP_TIMER9_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER1_IRQHandler) for TIMER1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER2_IRQHandler) for TIMER2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI10_15_IRQHandler) for EXTI10_15_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_gd32f450_470.o(RESET) refers to bsp_can.o(i.CAN1_RX0_IRQHandler) for CAN1_RX0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UART6_IRQHandler) for UART6_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_200m_25m_hxtal) for system_clock_200m_25m_hxtal
    data_convert.o(i.GetDouble) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    data_convert.o(i.GetNumber) refers to atoi.o(.text) for atoi
    data_convert.o(i.dec2HexStr) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.dtoc) refers to memseta.o(.text) for __aeabi_memclr4
    data_convert.o(i.dtoc) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.dtoc) refers to strlen.o(.text) for strlen
    data_convert.o(i.dtoc) refers to memcpya.o(.text) for __aeabi_memcpy
    data_convert.o(i.dtoc) refers to data_convert.o(.constdata) for .constdata
    data_convert.o(i.ftoa) refers to cdcmple.o(.text) for __aeabi_cdcmple
    data_convert.o(i.ftoa) refers to dadd.o(.text) for __aeabi_dsub
    data_convert.o(i.ftoa) refers to dfixi.o(.text) for __aeabi_d2iz
    data_convert.o(i.ftoa) refers to dflti.o(.text) for __aeabi_i2d
    data_convert.o(i.ftoa) refers to d2f.o(.text) for __aeabi_d2f
    data_convert.o(i.itoa) refers to memseta.o(.text) for __aeabi_memclr4
    data_convert.o(i.itoa) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.itoa) refers to strlen.o(.text) for strlen
    data_convert.o(i.itoa) refers to memcpya.o(.text) for __aeabi_memcpy
    data_convert.o(i.itoc) refers to memseta.o(.text) for __aeabi_memclr4
    data_convert.o(i.itoc) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.itoc) refers to strlen.o(.text) for strlen
    data_convert.o(i.itoc) refers to memcpya.o(.text) for __aeabi_memcpy
    data_convert.o(i.str2bin) refers to malloc.o(i.malloc) for malloc
    data_convert.o(i.str2bin) refers to strncpy.o(.text) for strncpy
    data_convert.o(i.str2bin) refers to data_convert.o(i.strbin2u16) for strbin2u16
    data_convert.o(i.str2bin) refers to strlen.o(.text) for strlen
    data_convert.o(i.str2bin) refers to malloc.o(i.free) for free
    data_convert.o(i.strInStrCount) refers to strstr.o(.text) for strstr
    data_convert.o(i.strReplace) refers to strlen.o(.text) for strlen
    data_convert.o(i.strReplace) refers to malloc.o(i.malloc) for malloc
    data_convert.o(i.strReplace) refers to strncmp.o(.text) for strncmp
    data_convert.o(i.strReplace) refers to strcat.o(.text) for strcat
    data_convert.o(i.strReplace) refers to strncat.o(.text) for strncat
    data_convert.o(i.strReplace) refers to strcpy.o(.text) for strcpy
    data_convert.o(i.strReplace) refers to malloc.o(i.free) for free
    data_convert.o(i.strReverse) refers to strlen.o(.text) for strlen
    data_convert.o(i.strSplit) refers to strtok.o(.text) for strtok
    data_convert.o(i.strbin2u16) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_GetAvailWriteSpace) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_GetAvailWriteSpace) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_GetBytesInBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_GetKey) refers to segger_rtt.o(i.SEGGER_RTT_Read) for SEGGER_RTT_Read
    segger_rtt.o(i.SEGGER_RTT_HasData) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_HasDataUp) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_Init) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_Read) refers to segger_rtt.o(i.SEGGER_RTT_ReadNoLock) for SEGGER_RTT_ReadNoLock
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_ReadUpBuffer) refers to segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) for SEGGER_RTT_ReadUpBufferNoLock
    segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.data) for _aTerminalId
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._PostTerminalSwitch) for _PostTerminalSwitch
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.data) for _ActiveTerminal
    segger_rtt.o(i.SEGGER_RTT_WaitKey) refers to segger_rtt.o(i.SEGGER_RTT_GetKey) for SEGGER_RTT_GetKey
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i.SEGGER_RTT_WriteNoLock) for SEGGER_RTT_WriteNoLock
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_WriteDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_WriteDownBuffer) refers to segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) for SEGGER_RTT_WriteDownBufferNoLock
    segger_rtt.o(i.SEGGER_RTT_WriteDownBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i._DoInit) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i._DoInit) refers to segger_rtt.o(.constdata) for _aInitStr
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(.data) for _aTerminalId
    segger_rtt.o(i._WriteBlocking) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i._WriteNoCheck) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt_printf.o(i.SEGGER_RTT_printf) refers to segger_rtt_printf.o(i.SEGGER_RTT_vprintf) for SEGGER_RTT_vprintf
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintInt) for _PrintInt
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(.constdata) for _aV2C
    segger_rtt_printf.o(i._StoreChar) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    nav.o(i.Out_Data_Up) refers to ddiv.o(.text) for __aeabi_ddiv
    nav.o(i.Out_Data_Up) refers to dmul.o(.text) for __aeabi_dmul
    nav.o(i.Out_Data_Up) refers to nav_app.o(.bss) for NAV_Data_Full
    nav.o(i.SendOBSGNSSData) refers to memseta.o(.text) for __aeabi_memclr
    nav.o(i.SendOBSGNSSData) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav.o(i.SendOBSGNSSData) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav.o(i.SendOBSGNSSData) refers to printfa.o(i.__0sprintf) for __2sprintf
    nav.o(i.SendOBSGNSSData) refers to strlen.o(.text) for strlen
    nav.o(i.SendOBSGNSSData) refers to nav_math.o(i.CalculateCheckSum) for CalculateCheckSum
    nav.o(i.SendOBSGNSSData) refers to strcat.o(.text) for strcat
    nav.o(i.SendOBSGNSSData) refers to nav_cli.o(i.Arm_SendMsg) for Arm_SendMsg
    nav.o(i.SendOBSGNSSData) refers to nav.o(.bss) for g_OBSGNSSBuff
    nav.o(i.SendOBSGNSSData) refers to nav.o(.conststring) for .conststring
    nav.o(i.SendOBSIMUData) refers to memseta.o(.text) for __aeabi_memclr
    nav.o(i.SendOBSIMUData) refers to printfa.o(i.__0sprintf) for __2sprintf
    nav.o(i.SendOBSIMUData) refers to strlen.o(.text) for strlen
    nav.o(i.SendOBSIMUData) refers to nav_math.o(i.CalculateCheckSum) for CalculateCheckSum
    nav.o(i.SendOBSIMUData) refers to strcat.o(.text) for strcat
    nav.o(i.SendOBSIMUData) refers to nav_cli.o(i.Arm_SendMsg) for Arm_SendMsg
    nav.o(i.SendOBSIMUData) refers to nav.o(.bss) for g_OBSIMUBuff
    nav.o(i.SendOBSIMUData) refers to nav.o(.conststring) for .conststring
    nav.o(i.SendSINSData) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav.o(i.SendSINSData) refers to memseta.o(.text) for __aeabi_memclr
    nav.o(i.SendSINSData) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav.o(i.SendSINSData) refers to printfa.o(i.__0sprintf) for __2sprintf
    nav.o(i.SendSINSData) refers to strlen.o(.text) for strlen
    nav.o(i.SendSINSData) refers to nav_math.o(i.CalculateCheckSum) for CalculateCheckSum
    nav.o(i.SendSINSData) refers to strcat.o(.text) for strcat
    nav.o(i.SendSINSData) refers to nav_cli.o(i.Arm_SendMsg) for Arm_SendMsg
    nav.o(i.SendSINSData) refers to nav.o(.bss) for g_SINSBuff
    nav.o(i.SendSINSData) refers to nav.o(.conststring) for .conststring
    nav.o(i.SetNavFunsionSource) refers to nav_app.o(.bss) for NAV_Data_Full
    nav.o(i.SetNavFunsionSource) refers to nav.o(.data) for g_NavFunsionSourceChangeFlag
    nav.o(i.SetNavRtkStatus) refers to nav_app.o(.bss) for NAV_Data_Full
    nav.o(i.SetNavRtkStatus) refers to nav.o(.data) for g_NavRtkStatusChangeFlag
    nav.o(i.SetNavStandardFlag) refers to nav_app.o(.bss) for NAV_Data_Full
    nav.o(i.SetNavStandardFlag) refers to nav.o(.data) for g_NavStandardFlagChangeFlag
    nav.o(i.SetNavStatus) refers to nav_app.o(.bss) for NAV_Data_Full
    nav.o(i.SetNavStatus) refers to nav.o(.data) for g_NavStatusChangeFlag
    nav.o(.data) refers to nav.o(.conststring) for .conststring
    nav_app.o(i.CorrHeading) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_app.o(i.CorrHeading) refers to dadd.o(.text) for __aeabi_dadd
    nav_app.o(i.CorrHeading) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_app.o(i.CorrHeading_PI) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_app.o(i.CorrHeading_PI) refers to dadd.o(.text) for __aeabi_dadd
    nav_app.o(i.CorrHeading_PI) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_app.o(i.NAV_function) refers to printfa.o(i.__0printf) for __2printf
    nav_app.o(i.NAV_function) refers to nav_imu.o(i.Get_Param_Data) for Get_Param_Data
    nav_app.o(i.NAV_function) refers to nav_imu.o(i.Get_IMU_Data) for Get_IMU_Data
    nav_app.o(i.NAV_function) refers to nav_gnss.o(i.Get_GNSS_Data) for Get_GNSS_Data
    nav_app.o(i.NAV_function) refers to nav_ods.o(i.Get_ODS_Data) for Get_ODS_Data
    nav_app.o(i.NAV_function) refers to nav_app.o(i.Param_Data_Init) for Param_Data_Init
    nav_app.o(i.NAV_function) refers to nav_imu.o(i.Load_Standard_Data) for Load_Standard_Data
    nav_app.o(i.NAV_function) refers to nav.o(i.SetNavStatus) for SetNavStatus
    nav_app.o(i.NAV_function) refers to nav_sins.o(i.SINS_Init) for SINS_Init
    nav_app.o(i.NAV_function) refers to nav_kf.o(i.KF_Init) for KF_Init
    nav_app.o(i.NAV_function) refers to nav_sins.o(i.SINS_Update) for SINS_Update
    nav_app.o(i.NAV_function) refers to nav_kf.o(i.KF_UP2) for KF_UP2
    nav_app.o(i.NAV_function) refers to nav_app.o(i.NavKalmanMode) for NavKalmanMode
    nav_app.o(i.NAV_function) refers to nav.o(i.Out_Data_Up) for Out_Data_Up
    nav_app.o(i.NAV_function) refers to nav_app.o(.data) for row
    nav_app.o(i.NAV_function) refers to main.o(.bss) for combineData
    nav_app.o(i.NAV_function) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_app.o(i.NAV_function) refers to gnss.o(.bss) for hGPSData
    nav_app.o(i.NAV_function_LD_TEST) refers to nav_imu.o(i.Get_Param_Data) for Get_Param_Data
    nav_app.o(i.NAV_function_LD_TEST) refers to nav_imu.o(i.Get_IMU_Data) for Get_IMU_Data
    nav_app.o(i.NAV_function_LD_TEST) refers to nav_gnss.o(i.Get_GNSS_Data) for Get_GNSS_Data
    nav_app.o(i.NAV_function_LD_TEST) refers to nav_ods.o(i.Get_ODS_Data) for Get_ODS_Data
    nav_app.o(i.NAV_function_LD_TEST) refers to nav_app.o(i.Param_Data_Init) for Param_Data_Init
    nav_app.o(i.NAV_function_LD_TEST) refers to nav_imu.o(i.Load_Standard_Data) for Load_Standard_Data
    nav_app.o(i.NAV_function_LD_TEST) refers to nav.o(i.SetNavStatus) for SetNavStatus
    nav_app.o(i.NAV_function_LD_TEST) refers to nav_sins.o(i.SINS_Init) for SINS_Init
    nav_app.o(i.NAV_function_LD_TEST) refers to nav_sins.o(i.Earth_UP) for Earth_UP
    nav_app.o(i.NAV_function_LD_TEST) refers to nav_mahony.o(i.MahonyUpdate_NoMAG) for MahonyUpdate_NoMAG
    nav_app.o(i.NAV_function_LD_TEST) refers to nav.o(i.Out_Data_Up) for Out_Data_Up
    nav_app.o(i.NAV_function_LD_TEST) refers to main.o(.bss) for combineData
    nav_app.o(i.NAV_function_LD_TEST) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_app.o(i.NAV_function_UAV) refers to nav_imu.o(i.Get_Param_Data) for Get_Param_Data
    nav_app.o(i.NAV_function_UAV) refers to nav_imu.o(i.Get_IMU_Data) for Get_IMU_Data
    nav_app.o(i.NAV_function_UAV) refers to nav_gnss.o(i.Get_GNSS_Data) for Get_GNSS_Data
    nav_app.o(i.NAV_function_UAV) refers to nav_app.o(i.Param_Data_Init) for Param_Data_Init
    nav_app.o(i.NAV_function_UAV) refers to nav_imu.o(i.Load_Standard_Data) for Load_Standard_Data
    nav_app.o(i.NAV_function_UAV) refers to nav.o(i.SetNavStatus) for SetNavStatus
    nav_app.o(i.NAV_function_UAV) refers to nav_sins.o(i.SINS_Init) for SINS_Init
    nav_app.o(i.NAV_function_UAV) refers to nav_mahony.o(i.MahonyUpdate) for MahonyUpdate
    nav_app.o(i.NAV_function_UAV) refers to nav.o(i.Out_Data_Up) for Out_Data_Up
    nav_app.o(i.NAV_function_UAV) refers to nav_app.o(.data) for g_NavIndex
    nav_app.o(i.NAV_function_UAV) refers to main.o(.bss) for combineData
    nav_app.o(i.NAV_function_UAV) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_app.o(i.NavDRMode) refers to nav_sins.o(i.SINS_Update) for SINS_Update
    nav_app.o(i.NavDRMode) refers to nav_kf.o(i.KF_UP2) for KF_UP2
    nav_app.o(i.NavDRMode) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_app.o(i.NavDRMode) refers to main.o(.bss) for combineData
    nav_app.o(i.NavKalmanMode) refers to nav_sins.o(i.SINS_Update) for SINS_Update
    nav_app.o(i.NavKalmanMode) refers to nav_kf.o(i.KF_UP2) for KF_UP2
    nav_app.o(i.NavKalmanMode) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_app.o(i.NavKalmanMode) refers to main.o(.bss) for combineData
    nav_app.o(i.Param_Data_Init) refers to main.o(.bss) for combineData
    nav_app.o(.data) refers to nav_app.o(.conststring) for .conststring
    nav_kf.o(i.CalcAngleAntAndIMU) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.CalcAngleAntAndIMU) refers to nav_kf.o(i.CalcCa2n) for CalcCa2n
    nav_kf.o(i.CalcAngleAntAndIMU) refers to nav_math.o(i.matmul) for matmul
    nav_kf.o(i.CalcAngleAntAndIMU) refers to nav_math.o(i.Cnb2att) for Cnb2att
    nav_kf.o(i.CalcCa2n) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.CalcCa2n) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.CalcCa2n) refers to nav_math.o(i.att2qnb) for att2qnb
    nav_kf.o(i.CalcCa2n) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_kf.o(i.CheckFusionStatusAndTime) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_kf.o(i.CheckFusionStatusAndTime) refers to nav_app.o(.data) for g_NAV_Funsion_Status_Time
    nav_kf.o(i.CheckStandardCompleteStatus) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_kf.o(i.CheckStandardCompleteStatus) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_kf.o(i.CheckStandardCompleteStatus) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_kf.o(i.CheckStandardCompleteStatus) refers to nav_kf.o(.bss) for bias_acc_min
    nav_kf.o(i.CheckStandardCompleteStatus) refers to nav_kf.o(.data) for b2gps_min
    nav_kf.o(i.CheckStandardCompleteStatus) refers to dadd.o(.text) for __aeabi_dsub
    nav_kf.o(i.GPSObsCalAndNoiseSet) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.GPSObsCalAndNoiseSet) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.GPSObsCalAndNoiseSet) refers to nav_kf.o(i.LeverarmTimeCorr2) for LeverarmTimeCorr2
    nav_kf.o(i.GPSObsCalAndNoiseSet) refers to nav_kf.o(i.SetGnssKalmanRmatrix) for SetGnssKalmanRmatrix
    nav_kf.o(i.GPSObsCalAndNoiseSet) refers to nav_kf.o(.data) for Zk_heading
    nav_kf.o(i.GPSObsCalAndNoiseSet) refers to nav_kf.o(.bss) for Zk_vn
    nav_kf.o(i.KF_Init) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.KF_Init) refers to nav_kf.o(i.Init_XPQ) for Init_XPQ
    nav_kf.o(i.KF_Init) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_kf.o(i.KF_Init) refers to nav_math.o(i.EyeMatrix) for EyeMatrix
    nav_kf.o(i.KF_Init) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.KF_Init) refers to nav_math.o(i.att2qnb) for att2qnb
    nav_kf.o(i.KF_Init) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_kf.o(i.KF_Init) refers to nav_kf.o(.data) for P0_odo
    nav_kf.o(i.KF_UP2) refers to nav_ods.o(i.ODS_Angle_Estimation) for ODS_Angle_Estimation
    nav_kf.o(i.KF_UP2) refers to nav_kf.o(i.SetFunsionStatusAndMeasureUpdate) for SetFunsionStatusAndMeasureUpdate
    nav_kf.o(i.KF_UP2) refers to nav_kf.o(i.ObsCalAndObsNoiseSet) for ObsCalAndObsNoiseSet
    nav_kf.o(i.KF_UP2) refers to nav_kf.o(i.Update_Phi_HP) for Update_Phi_HP
    nav_kf.o(i.KF_UP2) refers to nav_math.o(i.matmul) for matmul
    nav_kf.o(i.KF_UP2) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.KF_UP2) refers to dadd.o(.text) for __aeabi_dadd
    nav_kf.o(i.KF_UP2) refers to nav_kf.o(.bss) for FP
    nav_kf.o(i.KF_UP2) refers to nav_math.o(i.Mat3_Inv) for Mat3_Inv
    nav_kf.o(i.KF_UP2) refers to nav_kf.o(.data) for R_ZUPT
    nav_kf.o(i.KF_UP2) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_kf.o(i.KF_UP2) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.KF_UP2) refers to nav_math.o(i.Mat2_Inv) for Mat2_Inv
    nav_kf.o(i.KF_UP2) refers to nav_math.o(i.symmetry) for symmetry
    nav_kf.o(i.KF_UP2) refers to nav_kf.o(i.KfFeedback) for KfFeedback
    nav_kf.o(i.KF_UP2) refers to nav_kf.o(i.CheckStandardCompleteStatus) for CheckStandardCompleteStatus
    nav_kf.o(i.KF_UP2) refers to nav.o(i.SetNavStandardFlag) for SetNavStandardFlag
    nav_kf.o(i.KF_UP2) refers to nav_kf.o(i.NavStandardParm2Flash) for NavStandardParm2Flash
    nav_kf.o(i.KF_UP2) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_kf.o(i.KF_UP2) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_kf.o(i.KF_UP2) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_kf.o(i.KfFeedback) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.KfFeedback) refers to dfltui.o(.text) for __aeabi_ui2d
    nav_kf.o(i.KfFeedback) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.KfFeedback) refers to dadd.o(.text) for __aeabi_dsub
    nav_kf.o(i.KfFeedback) refers to nav_math.o(i.qdelphi) for qdelphi
    nav_kf.o(i.KfFeedback) refers to nav_math.o(i.qnb2att) for qnb2att
    nav_kf.o(i.KfFeedback) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_kf.o(i.KfFeedback) refers to nav_math.o(i.Mat_Tr) for Mat_Tr
    nav_kf.o(i.LeverarmTimeCorr2) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.LeverarmTimeCorr2) refers to dfltui.o(.text) for __aeabi_ui2d
    nav_kf.o(i.LeverarmTimeCorr2) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_kf.o(i.LeverarmTimeCorr2) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_kf.o(i.LeverarmTimeCorr2) refers to dadd.o(.text) for __aeabi_dsub
    nav_kf.o(i.LeverarmTimeCorr2) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_kf.o(i.LeverarmTimeCorr2) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.LeverarmTimeCorr2) refers to nav_app.o(i.CorrHeading_PI) for CorrHeading_PI
    nav_kf.o(i.LeverarmTimeCorr2) refers to nav_math.o(i.askew) for askew
    nav_kf.o(i.LeverarmTimeCorr2) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_kf.o(i.LeverarmTimeCorr2) refers to nav_math.o(i.matmul) for matmul
    nav_kf.o(i.LeverarmTimeCorr2) refers to nav_math.o(i.matrixSum) for matrixSum
    nav_kf.o(i.LeverarmTimeCorr2) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_kf.o(i.LeverarmTimeCorr2) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_kf.o(i.LeverarmTimeCorr2) refers to tan.o(i.__hardfp_tan) for __hardfp_tan
    nav_kf.o(i.LeverarmTimeCorr2) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_kf.o(i.LeverarmTimeCorr2) refers to nav_kf.o(.bss) for Hk_head
    nav_kf.o(i.NavStandardParm2Flash) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.NavStandardParm2Flash) refers to memcpya.o(.text) for __aeabi_memcpy
    nav_kf.o(i.NavStandardParm2Flash) refers to computerframeparse.o(i.comm_saveCaliData) for comm_saveCaliData
    nav_kf.o(i.NavStandardParm2Flash) refers to main.o(.bss) for combineData
    nav_kf.o(i.NavStandardParm2Flash) refers to computerframeparse.o(.bss) for caliData
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to nav_kf.o(i.GPSObsCalAndNoiseSet) for GPSObsCalAndNoiseSet
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to nav_kf.o(i.WheelObsCalAndNoiseSet) for WheelObsCalAndNoiseSet
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to tan.o(i.__hardfp_tan) for __hardfp_tan
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to dadd.o(.text) for __aeabi_dsub
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to nav_kf.o(.bss) for Zk_ZUPT
    nav_kf.o(i.ObsCalAndObsNoiseSet) refers to nav_kf.o(.data) for ZkPre_ZUPT_heading
    nav_kf.o(i.Q_MAT_UP) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.Q_MAT_UP) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.Q_MAT_UP) refers to nav_math.o(i.eyes) for eyes
    nav_kf.o(i.SetGPSFunsionPolicy) refers to nav.o(i.SetNavFunsionSource) for SetNavFunsionSource
    nav_kf.o(i.SetGPSFunsionPolicy) refers to nav_kf.o(.data) for countgps
    nav_kf.o(i.SetGnssKalmanRmatrix) refers to f2d.o(.text) for __aeabi_f2d
    nav_kf.o(i.SetGnssKalmanRmatrix) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_kf.o(i.SetGnssKalmanRmatrix) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.SetGnssKalmanRmatrix) refers to nav_kf.o(.bss) for R_vn
    nav_kf.o(i.TEST_UP) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.TEST_UP) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.TEST_UP) refers to f2d.o(.text) for __aeabi_f2d
    nav_kf.o(i.TEST_UP) refers to nav_ods.o(i.WheelSpeedOptimize) for WheelSpeedOptimize
    nav_kf.o(i.TEST_UP) refers to dadd.o(.text) for __aeabi_dadd
    nav_kf.o(i.TEST_UP) refers to nav_kf.o(.bss) for Cn2m
    nav_kf.o(i.TEST_UP) refers to nav_math.o(i.Mat_Tr) for Mat_Tr
    nav_kf.o(i.Update_Phi) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.Update_Phi) refers to dfltui.o(.text) for __aeabi_ui2d
    nav_kf.o(i.Update_Phi) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.Update_Phi) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_kf.o(i.Update_Phi) refers to nav_math.o(i.EyeMatrix) for EyeMatrix
    nav_kf.o(i.Update_Phi) refers to dadd.o(.text) for __aeabi_dadd
    nav_kf.o(i.Update_Phi) refers to nav_math.o(i.askew) for askew
    nav_kf.o(i.Update_Phi_HP) refers to dfltui.o(.text) for __aeabi_ui2d
    nav_kf.o(i.Update_Phi_HP) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.Update_Phi_HP) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_kf.o(i.Update_Phi_HP) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_kf.o(i.Update_Phi_HP) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_kf.o(i.Update_Phi_HP) refers to tan.o(i.__hardfp_tan) for __hardfp_tan
    nav_kf.o(i.Update_Phi_HP) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.Update_Phi_HP) refers to nav_math.o(i.askew) for askew
    nav_kf.o(i.Update_Phi_HP) refers to nav_math.o(i.matrixSum) for matrixSum
    nav_kf.o(i.Update_Phi_HP) refers to nav_math.o(i.matmul) for matmul
    nav_kf.o(i.Update_Phi_HP) refers to nav_kf.o(.bss) for M1
    nav_kf.o(i.Update_Phi_HP) refers to nav_math.o(i.EyeMatrix) for EyeMatrix
    nav_kf.o(i.WheelObsCalAndNoiseSet) refers to memseta.o(.text) for __aeabi_memclr4
    nav_kf.o(i.WheelObsCalAndNoiseSet) refers to nav_math.o(i.matmul) for matmul
    nav_kf.o(i.WheelObsCalAndNoiseSet) refers to nav_math.o(i.Mat_Tr) for Mat_Tr
    nav_kf.o(i.WheelObsCalAndNoiseSet) refers to nav_math.o(i.askew) for askew
    nav_kf.o(i.WheelObsCalAndNoiseSet) refers to dmul.o(.text) for __aeabi_dmul
    nav_kf.o(i.WheelObsCalAndNoiseSet) refers to dadd.o(.text) for __aeabi_dadd
    nav_kf.o(i.WheelObsCalAndNoiseSet) refers to nav_kf.o(.bss) for Cn2m
    nav_kf.o(i.WheelObsCalAndNoiseSet) refers to nav_kf.o(.data) for Zk_m_s
    nav_kf.o(.data) refers to nav_kf.o(.conststring) for .conststring
    nav_math.o(i.ABS) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_math.o(i.Cnb2att) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.Cnb2att) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.Cnb2att) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_math.o(i.Cnb2att) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    nav_math.o(i.GetDoubleParmsFormString) refers to strtok.o(.text) for strtok
    nav_math.o(i.GetDoubleParmsFormString) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    nav_math.o(i.GetFracFromDouble) refers to dfixi.o(.text) for __aeabi_d2iz
    nav_math.o(i.GetFracFromDouble) refers to dflti.o(.text) for __aeabi_i2d
    nav_math.o(i.GetFracFromDouble) refers to dadd.o(.text) for __aeabi_drsub
    nav_math.o(i.InnerDot) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.InnerDot) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.Mat2_Inv) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.Mat2_Inv) refers to dadd.o(.text) for __aeabi_dsub
    nav_math.o(i.Mat2_Inv) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    nav_math.o(i.Mat2_Inv) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.Mat2_Inv) refers to nav_math.o(.data) for test_Inv1
    nav_math.o(i.Mat3_Inv) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.Mat3_Inv) refers to dadd.o(.text) for __aeabi_dsub
    nav_math.o(i.Mat3_Inv) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    nav_math.o(i.Mat3_Inv) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.Mat3_Inv) refers to nav_math.o(.data) for test_Inv1
    nav_math.o(i.Qnb2Cnb) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.Qnb2Cnb) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.UpdateQnb) refers to nav_math.o(i.norm) for norm
    nav_math.o(i.UpdateQnb) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.UpdateQnb) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_math.o(i.UpdateQnb) refers to dadd.o(.text) for __aeabi_drsub
    nav_math.o(i.UpdateQnb) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_math.o(i.UpdateQnb) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.UpdateQnb) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_math.o(i.UpdateQnb) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_math.o(i.UpdateQnb) refers to nav_math.o(i.qnbmul) for qnbmul
    nav_math.o(i.UpdateQnb) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_math.o(i.WRAP_PI) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_math.o(i.WRAP_PI) refers to dadd.o(.text) for __aeabi_dsub
    nav_math.o(i.WRAP_PI) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_math.o(i.WRAP_PI) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_math.o(i.att2qnb) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.att2qnb) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_math.o(i.att2qnb) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_math.o(i.att2qnb) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.att2qnb) refers to dadd.o(.text) for __aeabi_dsub
    nav_math.o(i.covecef) refers to nav_math.o(i.xyz2enu) for xyz2enu
    nav_math.o(i.covecef) refers to nav_math.o(i.matmul) for matmul
    nav_math.o(i.covenu) refers to nav_math.o(i.xyz2enu) for xyz2enu
    nav_math.o(i.covenu) refers to nav_math.o(i.matmul) for matmul
    nav_math.o(i.cross3) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.cross3) refers to dadd.o(.text) for __aeabi_dsub
    nav_math.o(i.dot) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.dot) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.ecef2enu) refers to nav_math.o(i.xyz2enu) for xyz2enu
    nav_math.o(i.ecef2enu) refers to nav_math.o(i.matmul) for matmul
    nav_math.o(i.ecef2pos) refers to nav_math.o(i.dot) for dot
    nav_math.o(i.ecef2pos) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.ecef2pos) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.ecef2pos) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_math.o(i.ecef2pos) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.ecef2pos) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_math.o(i.ecef2pos) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_math.o(i.ecef2pos) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    nav_math.o(i.ecef2pos) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    nav_math.o(i.enu2ecef) refers to nav_math.o(i.xyz2enu) for xyz2enu
    nav_math.o(i.enu2ecef) refers to nav_math.o(i.matmul) for matmul
    nav_math.o(i.eyes) refers to nav_math.o(i.zeros) for zeros
    nav_math.o(i.get_D64) refers to nav_math.o(.data) for m_uMemory
    nav_math.o(i.get_F32) refers to nav_math.o(.data) for m_uMemory
    nav_math.o(i.get_Int32) refers to nav_math.o(.data) for m_uMemory
    nav_math.o(i.get_UInt32) refers to nav_math.o(.data) for m_uMemory
    nav_math.o(i.get_Ushort) refers to nav_math.o(.data) for m_uMemory
    nav_math.o(i.lubksb) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.lubksb) refers to dadd.o(.text) for __aeabi_drsub
    nav_math.o(i.lubksb) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    nav_math.o(i.lubksb) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.ludcmp) refers to memseta.o(.text) for __aeabi_memclr4
    nav_math.o(i.ludcmp) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_math.o(i.ludcmp) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_math.o(i.ludcmp) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.ludcmp) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.ludcmp) refers to dadd.o(.text) for __aeabi_drsub
    nav_math.o(i.ludcmp) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    nav_math.o(i.ludcmp) refers to nav_math.o(.bss) for glLudMat_vv
    nav_math.o(i.m2qnb) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.m2qnb) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_math.o(i.m2qnb) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.m2qnb) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_math.o(i.m2qnb) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.m2qnb) refers to nav_math.o(i.norm) for norm
    nav_math.o(i.matcpy) refers to memcpya.o(.text) for __aeabi_memcpy4
    nav_math.o(i.matinv) refers to memseta.o(.text) for __aeabi_memclr4
    nav_math.o(i.matinv) refers to nav_math.o(i.matcpy) for matcpy
    nav_math.o(i.matinv) refers to nav_math.o(i.ludcmp) for ludcmp
    nav_math.o(i.matinv) refers to nav_math.o(i.lubksb) for lubksb
    nav_math.o(i.matinv) refers to nav_math.o(.bss) for m_InvMat_indx
    nav_math.o(i.matmul) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    nav_math.o(i.matmul) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.matmul) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.matrixSum) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    nav_math.o(i.matrixSum) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.matrixSum) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.norm) refers to nav_math.o(i.InnerDot) for InnerDot
    nav_math.o(i.norm) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_math.o(i.normv3) refers to nav_math.o(i.norm) for norm
    nav_math.o(i.normv3) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_math.o(i.normv3) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.parse_split_fnum) refers to strtok.o(.text) for strtok
    nav_math.o(i.parse_split_fnum) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    nav_math.o(i.parse_split_fnum) refers to d2f.o(.text) for __aeabi_d2f
    nav_math.o(i.pos2ecef) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_math.o(i.pos2ecef) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_math.o(i.pos2ecef) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.pos2ecef) refers to dadd.o(.text) for __aeabi_drsub
    nav_math.o(i.pos2ecef) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_math.o(i.pos2ecef) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.qdelphi) refers to nav_math.o(i.zeros) for zeros
    nav_math.o(i.qdelphi) refers to nav_math.o(i.rv2q) for rv2q
    nav_math.o(i.qdelphi) refers to nav_math.o(i.qnbmul) for qnbmul
    nav_math.o(i.qdelphi) refers to malloc.o(i.free) for free
    nav_math.o(i.qmulv) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.qmulv) refers to dadd.o(.text) for __aeabi_dsub
    nav_math.o(i.qnb2att) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.qnb2att) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.qnb2att) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    nav_math.o(i.qnb2att) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    nav_math.o(i.qnbmul) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.qnbmul) refers to dadd.o(.text) for __aeabi_dsub
    nav_math.o(i.rotv) refers to nav_math.o(i.norm) for norm
    nav_math.o(i.rotv) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.rotv) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_math.o(i.rotv) refers to dadd.o(.text) for __aeabi_drsub
    nav_math.o(i.rotv) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_math.o(i.rotv) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.rotv) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_math.o(i.rotv) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_math.o(i.rv2m) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.rv2m) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.rv2m) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_math.o(i.rv2m) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.rv2m) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_math.o(i.rv2m) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_math.o(i.rv2m) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_math.o(i.rv2q) refers to nav_math.o(i.norm) for norm
    nav_math.o(i.rv2q) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.rv2q) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_math.o(i.rv2q) refers to dadd.o(.text) for __aeabi_drsub
    nav_math.o(i.rv2q) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_math.o(i.rv2q) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_math.o(i.rv2q) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_math.o(i.symmetry) refers to dadd.o(.text) for __aeabi_dadd
    nav_math.o(i.symmetry) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.xyz2enu) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_math.o(i.xyz2enu) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_math.o(i.xyz2enu) refers to dmul.o(.text) for __aeabi_dmul
    nav_math.o(i.zeros) refers to malloc.o(i.malloc) for malloc
    nav_math.o(.data) refers to nav_math.o(.conststring) for .conststring
    nav_ods.o(i.Caculate_ODS_angle) refers to f2d.o(.text) for __aeabi_f2d
    nav_ods.o(i.Caculate_ODS_angle) refers to dmul.o(.text) for __aeabi_dmul
    nav_ods.o(i.Caculate_ODS_angle) refers to dadd.o(.text) for __aeabi_dsub
    nav_ods.o(i.Caculate_ODS_angle) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_ods.o(i.Get_ODS_Data) refers to nav_ods.o(i.Caculate_ODS_angle) for Caculate_ODS_angle
    nav_ods.o(i.Get_ODS_Data) refers to f2d.o(.text) for __aeabi_f2d
    nav_ods.o(i.Get_ODS_Data) refers to dmul.o(.text) for __aeabi_dmul
    nav_ods.o(i.Get_ODS_Data) refers to nav_ods.o(i.WheelSpeedOptimize) for WheelSpeedOptimize
    nav_ods.o(i.Get_ODS_Data) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_ods.o(i.Get_ODS_Data) refers to dadd.o(.text) for __aeabi_dadd
    nav_ods.o(i.Get_ODS_Data) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_ods.o(i.Get_ODS_Data) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_ods.o(i.Get_ODS_Data) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_ods.o(i.Get_ODS_Data) refers to nav_ods.o(.data) for valid_wheelspeed
    nav_ods.o(i.ODS_Angle_Estimation) refers to memseta.o(.text) for __aeabi_memclr4
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_math.o(i.matmul) for matmul
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_math.o(i.Mat_Tr) for Mat_Tr
    nav_ods.o(i.ODS_Angle_Estimation) refers to dmul.o(.text) for __aeabi_dmul
    nav_ods.o(i.ODS_Angle_Estimation) refers to dadd.o(.text) for __aeabi_dadd
    nav_ods.o(i.ODS_Angle_Estimation) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_ods.o(i.ODS_Angle_Estimation) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_ods.o(i.ODS_Angle_Estimation) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    nav_ods.o(i.ODS_Angle_Estimation) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_ods.o(i.ODS_Angle_Estimation) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_math.o(i.askew) for askew
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_ods.o(.bss) for mis_R
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_math.o(i.Mat3_Inv) for Mat3_Inv
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_math.o(i.matrixSum) for matrixSum
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_math.o(i.symmetry) for symmetry
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_math.o(i.qdelphi) for qdelphi
    nav_ods.o(i.ODS_Angle_Estimation) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_math.o(i.qnb2att) for qnb2att
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_app.o(i.CorrHeading_PI) for CorrHeading_PI
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_ods.o(i.Scalar_KalmanFilte) for Scalar_KalmanFilte
    nav_ods.o(i.ODS_Angle_Estimation) refers to nav_kf.o(.data) for R_heading
    nav_ods.o(i.Scalar_KalmanFilte) refers to dadd.o(.text) for __aeabi_dadd
    nav_ods.o(i.Scalar_KalmanFilte) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_ods.o(i.Scalar_KalmanFilte) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_ods.o(i.Scalar_KalmanFilte) refers to dmul.o(.text) for __aeabi_dmul
    nav_ods.o(i.WheelSpeedOptimize) refers to dadd.o(.text) for __aeabi_dadd
    nav_ods.o(i.WheelSpeedOptimize) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_ods.o(.data) refers to nav_ods.o(.conststring) for .conststring
    nav_sins.o(i.CalEarthGn) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_sins.o(i.CalEarthGn) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_sins.o(i.CalEarthGn) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.CalEarthGn) refers to dadd.o(.text) for __aeabi_dadd
    nav_sins.o(i.Cnscl_1) refers to nav_math.o(i.cross3) for cross3
    nav_sins.o(i.Cnscl_1) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.Cnscl_1) refers to dadd.o(.text) for __aeabi_dadd
    nav_sins.o(i.Earth_UP) refers to memseta.o(.text) for __aeabi_memclr4
    nav_sins.o(i.Earth_UP) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_sins.o(i.Earth_UP) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_sins.o(i.Earth_UP) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_sins.o(i.Earth_UP) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_sins.o(i.Earth_UP) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.Earth_UP) refers to dadd.o(.text) for __aeabi_drsub
    nav_sins.o(i.Earth_UP) refers to nav_math.o(i.matrixSum) for matrixSum
    nav_sins.o(i.Earth_UP) refers to nav_math.o(i.cross3) for cross3
    nav_sins.o(i.Lever) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_sins.o(i.Lever) refers to nav_math.o(i.matmul) for matmul
    nav_sins.o(i.Lever) refers to nav_math.o(i.matrixSum) for matrixSum
    nav_sins.o(i.SINS_Init) refers to nav_sins.o(i.StartCoarseAlign) for StartCoarseAlign
    nav_sins.o(i.SINS_Init) refers to dadd.o(.text) for __aeabi_dsub
    nav_sins.o(i.SINS_Init) refers to nav_math.o(i.matmul) for matmul
    nav_sins.o(i.SINS_Init) refers to dflti.o(.text) for __aeabi_i2d
    nav_sins.o(i.SINS_Init) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.SINS_Init) refers to dfltui.o(.text) for __aeabi_ui2d
    nav_sins.o(i.SINS_Init) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_sins.o(i.SINS_Init) refers to nav_sins.o(i.Earth_Init) for Earth_Init
    nav_sins.o(i.SINS_Init) refers to nav_sins.o(i.Earth_UP) for Earth_UP
    nav_sins.o(i.SINS_UP) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.SINS_UP) refers to memseta.o(.text) for __aeabi_memclr4
    nav_sins.o(i.SINS_UP) refers to dadd.o(.text) for __aeabi_dadd
    nav_sins.o(i.SINS_UP) refers to nav_sins.o(i.Earth_UP) for Earth_UP
    nav_sins.o(i.SINS_UP) refers to nav_math.o(i.matmul) for matmul
    nav_sins.o(i.SINS_UP) refers to nav_math.o(i.cross3) for cross3
    nav_sins.o(i.SINS_UP) refers to nav_sins.o(.bss) for wnb_pre
    nav_sins.o(i.SINS_UP) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_sins.o(i.SINS_UP) refers to nav_math.o(i.norm) for norm
    nav_sins.o(i.SINS_UP) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_sins.o(i.SINS_UP) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_sins.o(i.SINS_UP) refers to nav_math.o(i.qnb2att) for qnb2att
    nav_sins.o(i.SINS_UP) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_sins.o(i.SINS_UP) refers to nav_math.o(i.Mat_Tr) for Mat_Tr
    nav_sins.o(i.SINS_UP_ATT) refers to memseta.o(.text) for __aeabi_memclr4
    nav_sins.o(i.SINS_UP_ATT) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.SINS_UP_ATT) refers to nav_math.o(i.rv2q) for rv2q
    nav_sins.o(i.SINS_UP_ATT) refers to nav_math.o(i.qnbmul) for qnbmul
    nav_sins.o(i.SINS_UP_ATT) refers to nav_math.o(i.norm) for norm
    nav_sins.o(i.SINS_UP_ATT) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_sins.o(i.SINS_UP_ATT) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_sins.o(i.SINS_UP_ATT) refers to nav_math.o(i.qnb2att) for qnb2att
    nav_sins.o(i.SINS_UP_ATT) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_sins.o(i.SINS_UP_ATT) refers to nav_math.o(i.Mat_Tr) for Mat_Tr
    nav_sins.o(i.SINS_UP_DR) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.SINS_UP_DR) refers to memseta.o(.text) for __aeabi_memclr4
    nav_sins.o(i.SINS_UP_DR) refers to dadd.o(.text) for __aeabi_dadd
    nav_sins.o(i.SINS_UP_DR) refers to nav_sins.o(i.Earth_UP) for Earth_UP
    nav_sins.o(i.SINS_UP_DR) refers to nav_math.o(i.matmul) for matmul
    nav_sins.o(i.SINS_UP_DR) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_sins.o(i.SINS_UP_DR) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_sins.o(i.SINS_UP_DR) refers to nav_math.o(i.qnb2att) for qnb2att
    nav_sins.o(i.SINS_UP_DR) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_sins.o(i.SINS_UP_DR) refers to nav_math.o(i.Mat_Tr) for Mat_Tr
    nav_sins.o(i.SINS_UP_HP) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.SINS_UP_HP) refers to memseta.o(.text) for __aeabi_memclr4
    nav_sins.o(i.SINS_UP_HP) refers to dadd.o(.text) for __aeabi_dadd
    nav_sins.o(i.SINS_UP_HP) refers to nav_sins.o(i.Cnscl_1) for Cnscl_1
    nav_sins.o(i.SINS_UP_HP) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_sins.o(i.SINS_UP_HP) refers to nav_sins.o(i.Earth_UP) for Earth_UP
    nav_sins.o(i.SINS_UP_HP) refers to nav_sins.o(i.SINS_UP_VEL) for SINS_UP_VEL
    nav_sins.o(i.SINS_UP_HP) refers to nav_sins.o(i.SINS_UP_ATT) for SINS_UP_ATT
    nav_sins.o(i.SINS_UP_HP) refers to nav_math.o(i.matmul) for matmul
    nav_sins.o(i.SINS_UP_VEL) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_sins.o(i.SINS_UP_VEL) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.SINS_UP_VEL) refers to nav_math.o(i.rv2m) for rv2m
    nav_sins.o(i.SINS_UP_VEL) refers to nav_math.o(i.qmulv) for qmulv
    nav_sins.o(i.SINS_UP_VEL) refers to nav_math.o(i.matmul) for matmul
    nav_sins.o(i.SINS_UP_VEL) refers to nav_math.o(i.matrixSum) for matrixSum
    nav_sins.o(i.SINS_Update) refers to memseta.o(.text) for __aeabi_memclr4
    nav_sins.o(i.SINS_Update) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_sins.o(i.SINS_Update) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_sins.o(i.SINS_Update) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.SINS_Update) refers to dadd.o(.text) for __aeabi_dsub
    nav_sins.o(i.SINS_Update) refers to nav_math.o(i.UpdateQnb) for UpdateQnb
    nav_sins.o(i.SINS_Update) refers to nav_math.o(i.cross3) for cross3
    nav_sins.o(i.SINS_Update) refers to nav_math.o(i.matmul) for matmul
    nav_sins.o(i.SINS_Update) refers to nav_sins.o(i.Earth_UP) for Earth_UP
    nav_sins.o(i.SINS_Update) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_sins.o(i.SINS_Update) refers to nav_math.o(i.qnb2att) for qnb2att
    nav_sins.o(i.SINS_Update) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_sins.o(i.SINS_Update) refers to nav_math.o(i.Mat_Tr) for Mat_Tr
    nav_sins.o(i.SINS_Update) refers to nav_math.o(i.qnbmul) for qnbmul
    nav_sins.o(i.SINS_Update) refers to nav_math.o(i.matrixSum) for matrixSum
    nav_sins.o(i.StartCoarseAlign) refers to memseta.o(.text) for __aeabi_memclr4
    nav_sins.o(i.StartCoarseAlign) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_sins.o(i.StartCoarseAlign) refers to dadd.o(.text) for __aeabi_dadd
    nav_sins.o(i.StartCoarseAlign) refers to dmul.o(.text) for __aeabi_dmul
    nav_sins.o(i.StartCoarseAlign) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_sins.o(i.StartCoarseAlign) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    nav_sins.o(i.StartCoarseAlign) refers to nav_magnet.o(i.MagInitHeading) for MagInitHeading
    nav_sins.o(i.StartCoarseAlign) refers to nav_math.o(i.att2qnb) for att2qnb
    nav_sins.o(i.StartCoarseAlign) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_sins.o(i.StartCoarseAlign) refers to nav_app.o(i.CorrHeading) for CorrHeading
    nav_sins.o(i.StartCoarseAlign) refers to nav_sins.o(.bss) for imu_V_sum
    nav_sins.o(i.StartCoarseAlign) refers to nav_sins.o(.data) for cunt
    nav_sins.o(i.StartCoarseAlign) refers to nav_math.o(i.Mat_Tr) for Mat_Tr
    nav_sins.o(i.StartCoarseAlign) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_sins.o(.data) refers to nav_sins.o(.conststring) for .conststring
    navlog.o(i.inav_get_loglevel) refers to navlog.o(.data) for osa_loglevel
    navlog.o(i.inav_set_loglevel) refers to printfa.o(i.__0printf) for __2printf
    navlog.o(i.inav_set_loglevel) refers to navlog.o(.data) for osa_loglevel
    navlog.o(.data) refers to navlog.o(.conststring) for .conststring
    nav_cli.o(i.Arm_SendMsg) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    nav_cli.o(i.CliReadConfig) refers to memseta.o(.text) for __aeabi_memclr4
    nav_cli.o(i.CliReadConfig) refers to printfa.o(i.__0sprintf) for __2sprintf
    nav_cli.o(i.CliReadConfig) refers to strcat.o(.text) for strcat
    nav_cli.o(i.CliReadConfig) refers to f2d.o(.text) for __aeabi_f2d
    nav_cli.o(i.CliReadConfig) refers to main.o(.bss) for combineData
    nav_cli.o(i.CliReadConfig) refers to strlen.o(.text) for strlen
    nav_cli.o(i.CliReadConfig) refers to nav_cli.o(i.Arm_SendMsg) for Arm_SendMsg
    nav_cli.o(i.CliReadConfig) refers to nav_app.o(.data) for g_NavIndex
    nav_cli.o(i.CliReadConfig) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_cli.o(i.CliSetRestartNav) refers to memseta.o(.text) for __aeabi_memclr4
    nav_cli.o(i.CliSetRestartNav) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_cli.o(i.CliSetStopNav) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_cli.o(i.CliShowBuildVersion) refers to memseta.o(.text) for __aeabi_memclr4
    nav_cli.o(i.CliShowBuildVersion) refers to strcat.o(.text) for strcat
    nav_cli.o(i.CliShowBuildVersion) refers to strlen.o(.text) for strlen
    nav_cli.o(i.CliShowBuildVersion) refers to nav_cli.o(i.Arm_SendMsg) for Arm_SendMsg
    nav_cli.o(i.CliShowErrorCmd) refers to memseta.o(.text) for __aeabi_memclr4
    nav_cli.o(i.CliShowErrorCmd) refers to printfa.o(i.__0sprintf) for __2sprintf
    nav_cli.o(i.CliShowErrorCmd) refers to strcat.o(.text) for strcat
    nav_cli.o(i.CliShowErrorCmd) refers to strlen.o(.text) for strlen
    nav_cli.o(i.CliShowErrorCmd) refers to nav_cli.o(i.Arm_SendMsg) for Arm_SendMsg
    nav_cli.o(i.CliShowHelp) refers to memseta.o(.text) for __aeabi_memclr4
    nav_cli.o(i.CliShowHelp) refers to strcat.o(.text) for strcat
    nav_cli.o(i.CliShowHelp) refers to printfa.o(i.__0sprintf) for __2sprintf
    nav_cli.o(i.CliShowHelp) refers to strlen.o(.text) for strlen
    nav_cli.o(i.CliShowHelp) refers to nav_cli.o(i.Arm_SendMsg) for Arm_SendMsg
    nav_cli.o(i.CliShowNavStatus) refers to memseta.o(.text) for __aeabi_memclr4
    nav_cli.o(i.CliShowNavStatus) refers to printfa.o(i.__0sprintf) for __2sprintf
    nav_cli.o(i.CliShowNavStatus) refers to strcat.o(.text) for strcat
    nav_cli.o(i.CliShowNavStatus) refers to strlen.o(.text) for strlen
    nav_cli.o(i.CliShowNavStatus) refers to nav_cli.o(i.Arm_SendMsg) for Arm_SendMsg
    nav_cli.o(i.CliShowNavStatus) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_cli.o(i.CliShowNavStatus) refers to nav_cli.o(.data) for g_NavStatusStaTxt
    nav_cli.o(i.CliWriteConfig) refers to strstr.o(.text) for strstr
    nav_cli.o(i.CliWriteConfig) refers to atoi.o(.text) for atoi
    nav_cli.o(i.CliWriteConfig) refers to computerframeparse.o(i.comm_saveCaliData) for comm_saveCaliData
    nav_cli.o(i.CliWriteConfig) refers to nav_math.o(i.parse_split_fnum) for parse_split_fnum
    nav_cli.o(i.CliWriteConfig) refers to memcpya.o(.text) for __aeabi_memcpy
    nav_cli.o(i.CliWriteConfig) refers to computerframeparse.o(i.comm_set_customPara) for comm_set_customPara
    nav_cli.o(i.CliWriteConfig) refers to f2d.o(.text) for __aeabi_f2d
    nav_cli.o(i.CliWriteConfig) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    nav_cli.o(i.CliWriteConfig) refers to nav_cli.o(i.CliShowErrorCmd) for CliShowErrorCmd
    nav_cli.o(i.CliWriteConfig) refers to nav_cli.o(i.CliReadConfig) for CliReadConfig
    nav_cli.o(i.CliWriteConfig) refers to main.o(.bss) for combineData
    nav_cli.o(i.CliWriteConfig) refers to computerframeparse.o(.bss) for caliData
    nav_cli.o(i.ParseStrCmd) refers to strstr.o(.text) for strstr
    nav_cli.o(i.ParseStrCmd) refers to nav_cli.o(i.CliShowHelp) for CliShowHelp
    nav_cli.o(i.ParseStrCmd) refers to nav_cli.o(i.CliShowBuildVersion) for CliShowBuildVersion
    nav_cli.o(i.ParseStrCmd) refers to nav_cli.o(i.CliShowNavStatus) for CliShowNavStatus
    nav_cli.o(i.ParseStrCmd) refers to nav_cli.o(i.CliShowAbormal) for CliShowAbormal
    nav_cli.o(i.ParseStrCmd) refers to nav_cli.o(i.CliShowNavSet) for CliShowNavSet
    nav_cli.o(i.ParseStrCmd) refers to nav_cli.o(i.CliSetRestartNav) for CliSetRestartNav
    nav_cli.o(i.ParseStrCmd) refers to nav_cli.o(i.CliSetStopNav) for CliSetStopNav
    nav_cli.o(i.ParseStrCmd) refers to nav_cli.o(i.CliReadConfig) for CliReadConfig
    nav_cli.o(i.ParseStrCmd) refers to nav_cli.o(i.CliWriteConfig) for CliWriteConfig
    nav_cli.o(i.ParseStrCmd) refers to nav_kf.o(i.NavStandardParm2Flash) for NavStandardParm2Flash
    nav_cli.o(i.ParseStrCmd) refers to nav_cli.o(i.CliShowErrorCmd) for CliShowErrorCmd
    nav_cli.o(i.ParseStrCmd) refers to main.o(.bss) for combineData
    nav_cli.o(i.ParseStrCmd) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_cli.o(.data) refers to nav_cli.o(.conststring) for .conststring
    nav_gnss.o(i.Get_GNSS_Data) refers to nav_gnss.o(i.is_gnss_update) for is_gnss_update
    nav_gnss.o(i.Get_GNSS_Data) refers to f2d.o(.text) for __aeabi_f2d
    nav_gnss.o(i.Get_GNSS_Data) refers to dadd.o(.text) for __aeabi_dsub
    nav_gnss.o(i.Get_GNSS_Data) refers to nav_app.o(i.CorrHeading) for CorrHeading
    nav_gnss.o(i.Get_GNSS_Data) refers to nav.o(i.SetNavRtkStatus) for SetNavRtkStatus
    nav_gnss.o(i.Get_GNSS_Data) refers to dmul.o(.text) for __aeabi_dmul
    nav_gnss.o(i.Get_GNSS_Data) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_gnss.o(i.Get_GNSS_Data) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    nav_gnss.o(i.Get_GNSS_Data) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    nav_gnss.o(i.Get_GNSS_Data) refers to nav_kf.o(.data) for R_heading
    nav_gnss.o(.data) refers to nav_gnss.o(.conststring) for .conststring
    nav_imu.o(i.Acc_data_check) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_imu.o(i.Acc_data_check) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_imu.o(i.Get_IMU_Data) refers to dmul.o(.text) for __aeabi_dmul
    nav_imu.o(i.Get_IMU_Data) refers to dadd.o(.text) for __aeabi_dsub
    nav_imu.o(i.Get_IMU_Data) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_imu.o(i.Get_IMU_Data) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_imu.o(i.Get_IMU_Data) refers to f2d.o(.text) for __aeabi_f2d
    nav_imu.o(i.Get_IMU_Data) refers to nav_imu.o(.data) for gyro_fog_old
    nav_imu.o(i.Get_IMU_Data) refers to nav_imu.o(i.Load_Calib_Parms) for Load_Calib_Parms
    nav_imu.o(i.Get_IMU_Data) refers to memseta.o(.text) for __aeabi_memclr4
    nav_imu.o(i.Get_IMU_Data) refers to nav_imu.o(i.Acc_data_check) for Acc_data_check
    nav_imu.o(i.Get_IMU_Data) refers to nav_imu.o(i.Gyro_data_check) for Gyro_data_check
    nav_imu.o(i.Get_IMU_Data) refers to nav.o(i.SetNavStatus) for SetNavStatus
    nav_imu.o(i.Get_IMU_Data) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_imu.o(i.Get_IMU_Data) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_imu.o(i.Get_IMU_Data) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_imu.o(i.Get_IMU_Data) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_imu.o(i.Get_IMU_Data) refers to memcpya.o(.text) for __aeabi_memcpy4
    nav_imu.o(i.Get_IMU_Data) refers to nav_imu.o(.constdata) for .constdata
    nav_imu.o(i.Get_IMU_Data) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    nav_imu.o(i.Get_IMU_Data) refers to nav_math.o(i.att2qnb) for att2qnb
    nav_imu.o(i.Get_IMU_Data) refers to nav_math.o(i.rv2q) for rv2q
    nav_imu.o(i.Get_IMU_Data) refers to nav_math.o(i.qnbmul) for qnbmul
    nav_imu.o(i.Get_IMU_Data) refers to nav_math.o(i.Qnb2Cnb) for Qnb2Cnb
    nav_imu.o(i.Get_IMU_Data) refers to nav_math.o(i.matmul) for matmul
    nav_imu.o(i.Get_IMU_Data) refers to nav_math.o(i.cross3) for cross3
    nav_imu.o(i.Get_IMU_Data) refers to nav_math.o(i.qnb2att) for qnb2att
    nav_imu.o(i.Get_Param_Data) refers to f2d.o(.text) for __aeabi_f2d
    nav_imu.o(i.Gyro_data_check) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_imu.o(i.Gyro_data_check) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_imu.o(i.InitZeroOffsetCompenstation) refers to dadd.o(.text) for __aeabi_dsub
    nav_imu.o(i.Load_Calib_Parms) refers to memcpya.o(.text) for __aeabi_memcpy4
    nav_imu.o(i.Load_Standard_Data) refers to nav.o(i.SetNavStandardFlag) for SetNavStandardFlag
    nav_imu.o(i.Load_Standard_Data) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    nav_imu.o(i.Load_Standard_Data) refers to cdcmple.o(.text) for __aeabi_cdcmple
    nav_imu.o(i.Load_Standard_Data) refers to main.o(.bss) for combineData
    nav_imu.o(i.install_data_check) refers to nav_math.o(i.norm) for norm
    nav_imu.o(i.install_data_check) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_imu.o(i.nav_calib) refers to memseta.o(.text) for __aeabi_memclr4
    nav_imu.o(i.nav_calib) refers to nav_math.o(i.matrixSum) for matrixSum
    nav_imu.o(i.nav_calib) refers to memcpya.o(.text) for __aeabi_memcpy4
    nav_imu.o(i.nav_calib) refers to nav_math.o(i.matinv) for matinv
    nav_imu.o(i.nav_calib) refers to nav_math.o(i.matmul) for matmul
    nav_imu.o(i.savebuff) refers to nav_imu.o(.data) for index
    nav_imu.o(i.savebuff) refers to nav_app.o(.bss) for NAV_Data_Full
    nav_imu.o(.data) refers to nav_imu.o(.conststring) for .conststring
    nav_magnet.o(i.MagInitHeading) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    nav_magnet.o(i.MagInitHeading) refers to dmul.o(.text) for __aeabi_dmul
    nav_magnet.o(i.MagInitHeading) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    nav_magnet.o(i.MagInitHeading) refers to dadd.o(.text) for __aeabi_dadd
    nav_magnet.o(i.MagInitHeading) refers to d2f.o(.text) for __aeabi_d2f
    nav_magnet.o(i.MagInitHeading) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    nav_magnet.o(i.MagInitHeading) refers to f2d.o(.text) for __aeabi_f2d
    nav_magnet.o(.data) refers to nav_magnet.o(.conststring) for .conststring
    nav_mahony.o(i.MahonyInit) refers to memseta.o(.text) for __aeabi_memclr4
    nav_mahony.o(i.MahonyUpdate) refers to memseta.o(.text) for __aeabi_memclr4
    nav_mahony.o(i.MahonyUpdate) refers to nav_math.o(i.matmul) for matmul
    nav_mahony.o(i.MahonyUpdate) refers to nav_math.o(i.cross3) for cross3
    nav_mahony.o(i.MahonyUpdate) refers to dmul.o(.text) for __aeabi_dmul
    nav_mahony.o(i.MahonyUpdate) refers to dadd.o(.text) for __aeabi_dadd
    nav_mahony.o(i.MahonyUpdate) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_mahony.o(i.MahonyUpdate) refers to nav_math.o(i.matrixSum) for matrixSum
    nav_mahony.o(i.MahonyUpdate) refers to nav_mahony.o(i.SetPidParmByMotion) for SetPidParmByMotion
    nav_mahony.o(i.MahonyUpdate) refers to nav_sins.o(i.SINS_UP_ATT) for SINS_UP_ATT
    nav_mahony.o(i.MahonyUpdate) refers to nav_mahony.o(.bss) for g_NAV_MAHONY
    nav_mahony.o(i.MahonyUpdate_NoMAG) refers to memseta.o(.text) for __aeabi_memclr4
    nav_mahony.o(i.MahonyUpdate_NoMAG) refers to nav_math.o(i.matmul) for matmul
    nav_mahony.o(i.MahonyUpdate_NoMAG) refers to nav_math.o(i.cross3) for cross3
    nav_mahony.o(i.MahonyUpdate_NoMAG) refers to dmul.o(.text) for __aeabi_dmul
    nav_mahony.o(i.MahonyUpdate_NoMAG) refers to dadd.o(.text) for __aeabi_dadd
    nav_mahony.o(i.MahonyUpdate_NoMAG) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    nav_mahony.o(i.MahonyUpdate_NoMAG) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_mahony.o(i.MahonyUpdate_NoMAG) refers to nav_sins.o(i.SINS_UP_ATT) for SINS_UP_ATT
    nav_mahony.o(i.MahonyUpdate_NoMAG) refers to nav_mahony.o(.bss) for g_NAV_MAHONY
    nav_mahony.o(i.SetPidParmByMotion) refers to memseta.o(.text) for __aeabi_memclr4
    nav_mahony.o(i.interplim) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    nav_mahony.o(i.interplim) refers to dadd.o(.text) for __aeabi_dsub
    nav_mahony.o(i.interplim) refers to ddiv.o(.text) for __aeabi_ddiv
    nav_mahony.o(i.interplim) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    nav_mahony.o(i.interplim) refers to dmul.o(.text) for __aeabi_dmul
    nav_mahony.o(.data) refers to nav_mahony.o(.conststring) for .conststring
    nav_uwb.o(.data) refers to nav_uwb.o(.conststring) for .conststring
    computerframeparse.o(i.IMU_test_data_send) refers to memseta.o(.text) for __aeabi_memclr4
    computerframeparse.o(i.IMU_test_data_send) refers to printfa.o(i.__0sprintf) for __2sprintf
    computerframeparse.o(i.IMU_test_data_send) refers to strlen.o(.text) for strlen
    computerframeparse.o(i.IMU_test_data_send) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.adj_paraSave) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.adj_paraSave) refers to computerframeparse.o(i.comm_saveCaliData) for comm_saveCaliData
    computerframeparse.o(i.adj_paraSave) refers to nav_app.o(.bss) for NAV_Data_Out
    computerframeparse.o(i.adj_paraSave) refers to computerframeparse.o(.data) for lastFlag
    computerframeparse.o(i.adj_paraSave) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.adj_paraSave) refers to main.o(.bss) for combineData
    computerframeparse.o(i.adj_paraSyn) refers to memcpya.o(.text) for __aeabi_memcpy
    computerframeparse.o(i.adj_paraSyn) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.adj_paraSyn) refers to main.o(.bss) for combineData
    computerframeparse.o(i.comm_axis_read) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_cali_ehco_rsp) refers to memcpya.o(.text) for __aeabi_memcpy
    computerframeparse.o(i.comm_cali_ehco_rsp) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.comm_cali_ehco_rsp) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.comm_calib) refers to transplant.o(i.calibAccel) for calibAccel
    computerframeparse.o(i.comm_calib) refers to memseta.o(.text) for __aeabi_memclr4
    computerframeparse.o(i.comm_calib) refers to printfa.o(i.__0sprintf) for __2sprintf
    computerframeparse.o(i.comm_calib) refers to strlen.o(.text) for strlen
    computerframeparse.o(i.comm_calib) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_calib) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.comm_calib) refers to transplant.o(i.calibGyro) for calibGyro
    computerframeparse.o(i.comm_calib) refers to computerframeparse.o(.data) for calibFlag
    computerframeparse.o(i.comm_calib) refers to main.o(.bss) for combineData
    computerframeparse.o(i.comm_calib) refers to computerframeparse.o(.bss) for accelTrans
    computerframeparse.o(i.comm_fm_update) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_fm_update) refers to strncpy.o(.text) for strncpy
    computerframeparse.o(i.comm_fm_update) refers to computerframeparse.o(.data) for hDefaultSetting
    computerframeparse.o(i.comm_fm_update) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_fm_update) refers to main.o(.bss) for combineData
    computerframeparse.o(i.comm_get_baseline) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_nav_para_syn) refers to memcpya.o(.text) for __aeabi_memcpy
    computerframeparse.o(i.comm_nav_para_syn) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_nav_para_syn) refers to main.o(.bss) for combineData
    computerframeparse.o(i.comm_output_disable) refers to main.o(.bss) for combineData
    computerframeparse.o(i.comm_output_enable) refers to main.o(.bss) for combineData
    computerframeparse.o(i.comm_para_ehco_rsp) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_para_ehco_rsp) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.comm_para_ehco_rsp) refers to computerframeparse.o(.bss) for frame
    computerframeparse.o(i.comm_param_clrbits) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_param_setbits) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_readAccelCaliPtr) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.comm_readGyroCaliPtr) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.comm_read_currentFreq) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_read_dev_type_rsp) refers to strlen.o(.text) for strlen
    computerframeparse.o(i.comm_read_dev_type_rsp) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_read_dev_type_rsp) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    computerframeparse.o(i.comm_read_dev_type_rsp) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_read_ver_rsp) refers to strlen.o(.text) for strlen
    computerframeparse.o(i.comm_read_ver_rsp) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_read_ver_rsp) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    computerframeparse.o(i.comm_read_ver_rsp) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_resume_defaultPara) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_resume_defaultPara) refers to computerframeparse.o(i.comm_set_customPara) for comm_set_customPara
    computerframeparse.o(i.comm_resume_defaultPara) refers to computerframeparse.o(.data) for hDefaultSetting
    computerframeparse.o(i.comm_resume_defaultPara) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_saveCaliData) refers to bsp_fmc.o(i.fmc_erase_sector_by_address) for fmc_erase_sector_by_address
    computerframeparse.o(i.comm_saveCaliData) refers to computerframeparse.o(i.hash32) for hash32
    computerframeparse.o(i.comm_saveCaliData) refers to bsp_fmc.o(i.fmc_write_8bit_data) for fmc_write_8bit_data
    computerframeparse.o(i.comm_saveCaliData) refers to computerframeparse.o(.data) for imu_type_save_addr
    computerframeparse.o(i.comm_saveCaliData) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.comm_send_end_frame) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.comm_set_customPara) refers to bsp_fmc.o(i.fmc_erase_sector_by_address) for fmc_erase_sector_by_address
    computerframeparse.o(i.comm_set_customPara) refers to computerframeparse.o(i.hash32) for hash32
    computerframeparse.o(i.comm_set_customPara) refers to bsp_fmc.o(i.fmc_write_8bit_data) for fmc_write_8bit_data
    computerframeparse.o(i.comm_set_customPara) refers to computerframeparse.o(.data) for save_addr
    computerframeparse.o(i.comm_set_customPara) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_store_init) refers to bsp_fmc.o(i.fmc_read_8bit_data) for fmc_read_8bit_data
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.hash32) for hash32
    computerframeparse.o(i.comm_store_init) refers to bsp_fmc.o(i.fmc_erase_sector_by_address) for fmc_erase_sector_by_address
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.comm_resume_defaultPara) for comm_resume_defaultPara
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.comm_fm_update) for comm_fm_update
    computerframeparse.o(i.comm_store_init) refers to memseta.o(.text) for __aeabi_memclr4
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.comm_saveCaliData) for comm_saveCaliData
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.adj_paraSyn) for adj_paraSyn
    computerframeparse.o(i.comm_store_init) refers to bsp_fmc.o(i.Uart_TxInit) for Uart_TxInit
    computerframeparse.o(i.comm_store_init) refers to bsp_fmc.o(i.Uart_RxInit) for Uart_RxInit
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(.data) for save_addr
    computerframeparse.o(i.comm_store_init) refers to main.o(.bss) for combineData
    computerframeparse.o(i.comm_test_switch) refers to strncmp.o(.text) for strncmp
    computerframeparse.o(i.comm_test_switch) refers to computerframeparse.o(.data) for calibFlag
    computerframeparse.o(i.comm_test_switch) refers to main.o(.bss) for combineData
    computerframeparse.o(i.comm_write_rsp) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_test_switch) for comm_test_switch
    computerframeparse.o(i.frameParse) refers to protocol.o(i.iPMV_protocolParse) for iPMV_protocolParse
    computerframeparse.o(i.frameParse) refers to memcmp.o(.text) for memcmp
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_output_disable) for comm_output_disable
    computerframeparse.o(i.frameParse) refers to ymodem.o(i.rym_fm_update) for rym_fm_update
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_cali_ehco_rsp) for comm_cali_ehco_rsp
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_write_rsp) for comm_write_rsp
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_saveCaliData) for comm_saveCaliData
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_send_end_frame) for comm_send_end_frame
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_set_customPara) for comm_set_customPara
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_resume_defaultPara) for comm_resume_defaultPara
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_para_ehco_rsp) for comm_para_ehco_rsp
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_read_ver_rsp) for comm_read_ver_rsp
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_read_dev_type_rsp) for comm_read_dev_type_rsp
    computerframeparse.o(i.frameParse) refers to data_convert.o(i.hex2Float) for hex2Float
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_param_setbits) for comm_param_setbits
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.frameParse) refers to main.o(.bss) for combineData
    computerframeparse.o(i.miscell_handle) refers to computerframeparse.o(i.wheel_is_running) for wheel_is_running
    computerframeparse.o(i.miscell_handle) refers to computerframeparse.o(i.adj_paraSave) for adj_paraSave
    computerframeparse.o(i.navi_test_statusRd) refers to computerframeparse.o(.data) for naviTestFlg
    computerframeparse.o(i.protocol_send) refers to frame_analysis.o(i.frame_pack_and_send) for frame_pack_and_send
    computerframeparse.o(i.protocol_send) refers to frame_analysis.o(i.frame_miscel_send) for frame_miscel_send
    computerframeparse.o(i.protocol_send) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.protocol_send) refers to computerframeparse.o(.data) for sendCnt
    computerframeparse.o(i.protocol_send) refers to main.o(.bss) for combineData
    computerframeparse.o(i.protocol_send) refers to gnss.o(.bss) for hGPSData
    computerframeparse.o(i.protocol_send) refers to nav_app.o(.bss) for NAV_Data_Out
    computerframeparse.o(i.protocol_send) refers to main.o(.data) for goutputmode
    computerframeparse.o(i.wheel_is_running) refers to main.o(.bss) for combineData
    computerframeparse.o(i.wheel_is_running) refers to computerframeparse.o(.data) for lastCanCounter
    computerframeparse.o(i.wheel_is_running) refers to ins_data.o(.data) for g_LEDIndicatorState
    frame_analysis.o(i.frame_fill_ifog) refers to frame_analysis.o(i.xor_check) for xor_check
    frame_analysis.o(i.frame_fill_ifog) refers to dflti.o(.text) for __aeabi_i2d
    frame_analysis.o(i.frame_fill_ifog) refers to ddiv.o(.text) for __aeabi_ddiv
    frame_analysis.o(i.frame_fill_ifog) refers to frame_analysis.o(.bss) for iFogParseData
    frame_analysis.o(i.frame_fill_imu) refers to frame_analysis.o(i.sum_check) for sum_check
    frame_analysis.o(i.frame_fill_imu) refers to memcpya.o(.text) for __aeabi_memcpy
    frame_analysis.o(i.frame_fill_imu) refers to dflti.o(.text) for __aeabi_i2d
    frame_analysis.o(i.frame_fill_imu) refers to dmul.o(.text) for __aeabi_dmul
    frame_analysis.o(i.frame_fill_imu) refers to ddiv.o(.text) for __aeabi_ddiv
    frame_analysis.o(i.frame_fill_imu) refers to frame_analysis.o(.bss) for imu_info
    frame_analysis.o(i.frame_form) refers to computerframeparse.o(i.comm_read_currentFreq) for comm_read_currentFreq
    frame_analysis.o(i.frame_form) refers to frame_analysis.o(i.frame_navi_and_gnss_send) for frame_navi_and_gnss_send
    frame_analysis.o(i.frame_form) refers to frame_analysis.o(i.frame_imu_and_gnss_send) for frame_imu_and_gnss_send
    frame_analysis.o(i.frame_form) refers to frame_analysis.o(i.frame_miscel_send) for frame_miscel_send
    frame_analysis.o(i.frame_form) refers to frame_analysis.o(.data) for tCnt
    frame_analysis.o(i.frame_form) refers to main.o(.bss) for combineData
    frame_analysis.o(i.frame_form) refers to gnss.o(.bss) for hGPSData
    frame_analysis.o(i.frame_form) refers to nav_app.o(.bss) for NAV_Data_Out
    frame_analysis.o(i.frame_iPMV_pack_and_send) refers to frame_analysis.o(i.xor_check) for xor_check
    frame_analysis.o(i.frame_iPMV_pack_and_send) refers to frame_analysis.o(.bss) for moduleStatus
    frame_analysis.o(i.frame_iPMV_pack_and_send) refers to main.o(.bss) for combineData
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to computerframeparse.o(i.comm_axis_read) for comm_axis_read
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to d2f.o(.text) for __aeabi_d2f
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to dadd.o(.text) for __aeabi_dadd
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to ddiv.o(.text) for __aeabi_ddiv
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to computerframeparse.o(i.comm_read_currentFreq) for comm_read_currentFreq
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to f2d.o(.text) for __aeabi_f2d
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to frame_analysis.o(.data) for axisInfo
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to frame_analysis.o(.constdata) for axisTab
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to main.o(.bss) for combineData
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to frame_analysis.o(.bss) for navi_test_info
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to printfa.o(i.__0sprintf) for __2sprintf
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to strlen.o(.text) for strlen
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to memcpya.o(.text) for __aeabi_memcpy
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to frame_analysis.o(.conststring) for .conststring
    frame_analysis.o(i.frame_imu_and_gnss_send) refers to gnss.o(.bss) for hGPSData
    frame_analysis.o(i.frame_init) refers to memseta.o(.text) for __aeabi_memclr
    frame_analysis.o(i.frame_init) refers to frame_analysis.o(.bss) for rs422_frame
    frame_analysis.o(i.frame_miscel_send) refers to computerframeparse.o(i.comm_read_currentFreq) for comm_read_currentFreq
    frame_analysis.o(i.frame_miscel_send) refers to d2f.o(.text) for __aeabi_d2f
    frame_analysis.o(i.frame_miscel_send) refers to f2d.o(.text) for __aeabi_f2d
    frame_analysis.o(i.frame_miscel_send) refers to frame_analysis.o(.bss) for navi_test_info
    frame_analysis.o(i.frame_miscel_send) refers to frame_analysis.o(.data) for xgpsTrueTime
    frame_analysis.o(i.frame_miscel_send) refers to main.o(.bss) for combineData
    frame_analysis.o(i.frame_miscel_send) refers to main.o(.data) for goutputmode
    frame_analysis.o(i.frame_miscel_send) refers to printfa.o(i.__0sprintf) for __2sprintf
    frame_analysis.o(i.frame_miscel_send) refers to strlen.o(.text) for strlen
    frame_analysis.o(i.frame_miscel_send) refers to memcpya.o(.text) for __aeabi_memcpy
    frame_analysis.o(i.frame_miscel_send) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    frame_analysis.o(i.frame_miscel_send) refers to frame_analysis.o(.conststring) for .conststring
    frame_analysis.o(i.frame_miscel_send) refers to ins_output.o(.data) for gfog0
    frame_analysis.o(i.frame_miscel_send) refers to gdwatch.o(.data) for ggdworgdata_packet
    frame_analysis.o(i.frame_miscel_send) refers to gdwatch.o(.bss) for gdriverdatalist
    frame_analysis.o(i.frame_navi_and_gnss_send) refers to transplant.o(i.rtc_update) for rtc_update
    frame_analysis.o(i.frame_navi_and_gnss_send) refers to f2d.o(.text) for __aeabi_f2d
    frame_analysis.o(i.frame_navi_and_gnss_send) refers to computerframeparse.o(i.comm_read_currentFreq) for comm_read_currentFreq
    frame_analysis.o(i.frame_navi_and_gnss_send) refers to printfa.o(i.__0sprintf) for __2sprintf
    frame_analysis.o(i.frame_navi_and_gnss_send) refers to strlen.o(.text) for strlen
    frame_analysis.o(i.frame_navi_and_gnss_send) refers to memcpya.o(.text) for __aeabi_memcpy
    frame_analysis.o(i.frame_navi_and_gnss_send) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    frame_analysis.o(i.frame_navi_and_gnss_send) refers to frame_analysis.o(.bss) for navi_test_info
    frame_analysis.o(i.frame_navi_and_gnss_send) refers to frame_analysis.o(.data) for xgpsTime
    frame_analysis.o(i.frame_navi_and_gnss_send) refers to frame_analysis.o(.conststring) for .conststring
    frame_analysis.o(i.frame_pack_and_send) refers to ddiv.o(.text) for __aeabi_ddiv
    frame_analysis.o(i.frame_pack_and_send) refers to dmul.o(.text) for __aeabi_dmul
    frame_analysis.o(i.frame_pack_and_send) refers to dfixi.o(.text) for __aeabi_d2iz
    frame_analysis.o(i.frame_pack_and_send) refers to computerframeparse.o(i.comm_axis_read) for comm_axis_read
    frame_analysis.o(i.frame_pack_and_send) refers to frame_analysis.o(.bss) for rs422_frame
    frame_analysis.o(i.frame_pack_and_send) refers to frame_analysis.o(.data) for axisInfo
    frame_analysis.o(i.frame_pack_and_send) refers to frame_analysis.o(.constdata) for axisTab
    frame_analysis.o(i.frame_pack_and_send) refers to main.o(.bss) for combineData
    frame_analysis.o(i.frame_pack_and_send) refers to dflti.o(.text) for __aeabi_i2d
    frame_analysis.o(i.frame_pack_and_send) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    frame_analysis.o(i.frame_pack_and_send) refers to dadd.o(.text) for __aeabi_dadd
    frame_analysis.o(i.frame_pack_and_send) refers to dfixui.o(.text) for __aeabi_d2uiz
    frame_analysis.o(i.frame_pack_and_send) refers to computerframeparse.o(i.comm_read_currentFreq) for comm_read_currentFreq
    frame_analysis.o(i.frame_pack_and_send) refers to frame_analysis.o(i.xor_check) for xor_check
    frame_analysis.o(i.frame_pack_and_send) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    frame_analysis.o(i.frame_pack_and_send) refers to memseta.o(.text) for __aeabi_memclr
    frame_analysis.o(i.frame_pack_and_send) refers to memcpya.o(.text) for __aeabi_memcpy
    frame_analysis.o(i.frame_pack_and_send) refers to f2d.o(.text) for __aeabi_f2d
    frame_analysis.o(i.frame_pack_and_send) refers to nav_app.o(.bss) for NAV_Data_Out
    frame_analysis.o(i.frame_pack_and_send) refers to main.o(.data) for goutputmode
    protocol.o(i.analysisRxdata) refers to memcpya.o(.text) for __aeabi_memcpy
    protocol.o(i.analysisRxdata) refers to gdwatch.o(i.mcusendtopcdriversdata) for mcusendtopcdriversdata
    protocol.o(i.analysisRxdata) refers to gd32f4xx_it.o(.data) for grxst
    protocol.o(i.analysisRxdata) refers to gd32f4xx_it.o(.bss) for grxbuffer
    protocol.o(i.analysisRxdata) refers to main.o(.data) for goutputmode
    protocol.o(i.analysisRxdata) refers to fpgad.o(.bss) for gframeParsebuf
    protocol.o(i.analysisRxdata) refers to protocol.o(.bss) for gframeParsebuf
    protocol.o(i.analysisRxdata) refers to setparabao.o(i.UartDmaRecSetPara) for UartDmaRecSetPara
    protocol.o(i.analysisRxdata) refers to nav_cli.o(i.ParseStrCmd) for ParseStrCmd
    protocol.o(i.iPMV_GIMU_BIN_Send) refers to ddiv.o(.text) for __aeabi_ddiv
    protocol.o(i.iPMV_GIMU_BIN_Send) refers to dmul.o(.text) for __aeabi_dmul
    protocol.o(i.iPMV_GIMU_BIN_Send) refers to dfixi.o(.text) for __aeabi_d2iz
    protocol.o(i.iPMV_GIMU_BIN_Send) refers to frame_analysis.o(i.xor_check) for xor_check
    protocol.o(i.iPMV_GIMU_BIN_Send) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    protocol.o(i.iPMV_GIMU_BIN_Send) refers to protocol.o(.bss) for gimu_bin_data
    protocol.o(i.iPMV_GIMU_BIN_Send) refers to main.o(.bss) for combineData
    protocol.o(i.iPMV_GINSSTD_BIN_Send) refers to dmul.o(.text) for __aeabi_dmul
    protocol.o(i.iPMV_GINSSTD_BIN_Send) refers to dfixui.o(.text) for __aeabi_d2uiz
    protocol.o(i.iPMV_GINSSTD_BIN_Send) refers to frame_analysis.o(i.xor_check) for xor_check
    protocol.o(i.iPMV_GINSSTD_BIN_Send) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    protocol.o(i.iPMV_GINSSTD_BIN_Send) refers to protocol.o(.bss) for ginsstd_bin_data
    protocol.o(i.iPMV_GINS_BIN_Send) refers to dfltui.o(.text) for __aeabi_ui2d
    protocol.o(i.iPMV_GINS_BIN_Send) refers to ddiv.o(.text) for __aeabi_ddiv
    protocol.o(i.iPMV_GINS_BIN_Send) refers to dmul.o(.text) for __aeabi_dmul
    protocol.o(i.iPMV_GINS_BIN_Send) refers to dfixi.o(.text) for __aeabi_d2iz
    protocol.o(i.iPMV_GINS_BIN_Send) refers to frame_analysis.o(i.xor_check) for xor_check
    protocol.o(i.iPMV_GINS_BIN_Send) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    protocol.o(i.iPMV_GINS_BIN_Send) refers to protocol.o(.bss) for gins_bin_data
    protocol.o(i.iPMV_GINS_BIN_Send) refers to main.o(.bss) for combineData
    protocol.o(i.iPMV_dataParse) refers to strstr.o(.text) for strstr
    protocol.o(i.iPMV_dataParse) refers to strncmp.o(.text) for strncmp
    protocol.o(i.iPMV_dataParse) refers to strtod.o(i.__hardfp_strtod) for __hardfp_strtod
    protocol.o(i.iPMV_dataParse) refers to protocol.o(.data) for iPMV_type
    protocol.o(i.iPMV_protocolParse) refers to strtok.o(.text) for strtok
    protocol.o(i.iPMV_protocolParse) refers to protocol.o(i.iPMV_dataParse) for iPMV_dataParse
    protocol.o(i.iPMV_protocol_report) refers to protocol.o(i.iPMV_GINS_BIN_Send) for iPMV_GINS_BIN_Send
    protocol.o(i.iPMV_protocol_report) refers to protocol.o(i.iPMV_GIMU_BIN_Send) for iPMV_GIMU_BIN_Send
    protocol.o(i.iPMV_protocol_report) refers to protocol.o(i.iPMV_GINSSTD_BIN_Send) for iPMV_GINSSTD_BIN_Send
    protocol.o(i.iPMV_protocol_report) refers to main.o(.bss) for combineData
    protocol.o(i.iPMV_protocol_report) refers to protocol.o(.data) for iPMV_sendCnt
    protocol.o(i.protocol_crc32_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    protocol.o(i.protocol_crc32_init) refers to gd32f4xx_crc.o(i.crc_data_register_reset) for crc_data_register_reset
    protocol.o(i.protocol_fillGipotData) refers to dfltui.o(.text) for __aeabi_ui2d
    protocol.o(i.protocol_fillGipotData) refers to dmul.o(.text) for __aeabi_dmul
    protocol.o(i.protocol_fillGipotData) refers to f2d.o(.text) for __aeabi_f2d
    protocol.o(i.protocol_fillGipotData) refers to memcpya.o(.text) for __aeabi_memcpy4
    protocol.o(i.protocol_fillGipotData) refers to protocol.o(.bss) for ins_gipot_data
    protocol.o(i.protocol_fillGrimuData) refers to dfltui.o(.text) for __aeabi_ui2d
    protocol.o(i.protocol_fillGrimuData) refers to dmul.o(.text) for __aeabi_dmul
    protocol.o(i.protocol_fillGrimuData) refers to protocol.o(.bss) for ins_grimu_data
    protocol.o(i.protocol_fillGrimuData) refers to frame_analysis.o(.bss) for imuParseData
    protocol.o(i.protocol_fillRawimuData) refers to dfltui.o(.text) for __aeabi_ui2d
    protocol.o(i.protocol_fillRawimuData) refers to dmul.o(.text) for __aeabi_dmul
    protocol.o(i.protocol_fillRawimuData) refers to dfixi.o(.text) for __aeabi_d2iz
    protocol.o(i.protocol_fillRawimuData) refers to gd32f4xx_crc.o(i.crc_block_data_calculate) for crc_block_data_calculate
    protocol.o(i.protocol_fillRawimuData) refers to protocol.o(.bss) for ins_rawimu_data
    protocol.o(i.protocol_fillRawimuData) refers to protocol.o(.data) for IMUStatus
    protocol.o(i.protocol_gnggaDataGet) refers to memcpya.o(.text) for __aeabi_memcpy
    protocol.o(i.protocol_gnggaDataGet) refers to protocol.o(.bss) for gnggaSendBuff
    protocol.o(i.protocol_gnggaDataGet) refers to protocol.o(.data) for gnggaSendLen
    protocol.o(i.protocol_gnrmcDataGet) refers to memcpya.o(.text) for __aeabi_memcpy
    protocol.o(i.protocol_gnrmcDataGet) refers to protocol.o(.bss) for gnrmcSendBuff
    protocol.o(i.protocol_gnrmcDataGet) refers to protocol.o(.data) for gnrmcSendLen
    protocol.o(i.protocol_report) refers to computerframeparse.o(i.comm_read_dataMode) for comm_read_dataMode
    protocol.o(i.protocol_report) refers to computerframeparse.o(i.comm_read_currentFrameType) for comm_read_currentFrameType
    protocol.o(i.protocol_report) refers to printfa.o(i.__0sprintf) for __2sprintf
    protocol.o(i.protocol_report) refers to protocol.o(i.HexToAscii) for HexToAscii
    protocol.o(i.protocol_report) refers to memseta.o(.text) for __aeabi_memclr4
    protocol.o(i.protocol_report) refers to memcpya.o(.text) for __aeabi_memcpy4
    protocol.o(i.protocol_report) refers to strlen.o(.text) for strlen
    protocol.o(i.protocol_report) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    protocol.o(i.protocol_report) refers to protocol.o(.bss) for ins_gipot_data
    protocol.o(i.protocol_report) refers to protocol.o(.conststring) for .conststring
    protocol.o(i.protocol_report) refers to protocol.o(.data) for NavDataSendedCnt
    setparabao.o(i.InitParaToAlgorithm) refers to computerframeparse.o(i.comm_param_setbits) for comm_param_setbits
    setparabao.o(i.InitParaToAlgorithm) refers to setparabao.o(i.SetCoordToAlgorithm) for SetCoordToAlgorithm
    setparabao.o(i.InitParaToAlgorithm) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    setparabao.o(i.InitParaToAlgorithm) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.InitParaToAlgorithm) refers to computerframeparse.o(.bss) for hSetting
    setparabao.o(i.InitParaToAlgorithm) refers to main.o(.bss) for combineData
    setparabao.o(i.ParaUpdateHandle) refers to firmwareupdatefile.o(i.Drv_FlashErase) for Drv_FlashErase
    setparabao.o(i.ParaUpdateHandle) refers to firmwareupdatefile.o(i.Drv_FlashWrite) for Drv_FlashWrite
    setparabao.o(i.ParaUpdateHandle) refers to setparabao.o(.data) for uiLastBaoInDex
    setparabao.o(i.ReadPara) refers to setparabao.o(i.ReadPara_0) for ReadPara_0
    setparabao.o(i.ReadPara) refers to setparabao.o(i.ReadPara_1) for ReadPara_1
    setparabao.o(i.ReadPara) refers to setparabao.o(i.ReadPara_2) for ReadPara_2
    setparabao.o(i.ReadPara) refers to setparabao.o(i.ReadPara_3) for ReadPara_3
    setparabao.o(i.ReadPara) refers to setparabao.o(i.ReadPara_4) for ReadPara_4
    setparabao.o(i.ReadPara0_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadPara0_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadPara0_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.ReadPara1_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadPara1_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadPara1_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.ReadPara2_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadPara2_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadPara2_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.ReadPara3_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadPara3_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadPara3_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.ReadPara4_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadPara4_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadPara4_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.ReadParaFromFlash) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadParaFromFlash) refers to firmwareupdatefile.o(i.Drv_FlashRead_8bit) for Drv_FlashRead_8bit
    setparabao.o(i.ReadParaFromFlash) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadParaFromFlash) refers to firmwareupdatefile.o(i.Drv_FlashErase) for Drv_FlashErase
    setparabao.o(i.ReadParaFromFlash) refers to firmwareupdatefile.o(i.Drv_FlashWrite) for Drv_FlashWrite
    setparabao.o(i.ReadParaFromFlash) refers to setparabao.o(i.InitParaToAlgorithm) for InitParaToAlgorithm
    setparabao.o(i.ReadParaFromFlash) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.ReadPara_0) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadPara_0) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadPara_0) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.ReadPara_0) refers to setparabao.o(i.ReadPara0_SetHead) for ReadPara0_SetHead
    setparabao.o(i.ReadPara_0) refers to setparabao.o(i.ReadPara0_SetEnd) for ReadPara0_SetEnd
    setparabao.o(i.ReadPara_0) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.ReadPara_0) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.ReadPara_1) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadPara_1) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadPara_1) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.ReadPara_1) refers to setparabao.o(i.ReadPara1_SetHead) for ReadPara1_SetHead
    setparabao.o(i.ReadPara_1) refers to setparabao.o(i.ReadPara1_SetEnd) for ReadPara1_SetEnd
    setparabao.o(i.ReadPara_1) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.ReadPara_1) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.ReadPara_2) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadPara_2) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadPara_2) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.ReadPara_2) refers to setparabao.o(i.ReadPara2_SetHead) for ReadPara2_SetHead
    setparabao.o(i.ReadPara_2) refers to setparabao.o(i.ReadPara2_SetEnd) for ReadPara2_SetEnd
    setparabao.o(i.ReadPara_2) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.ReadPara_2) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.ReadPara_3) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadPara_3) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadPara_3) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.ReadPara_3) refers to setparabao.o(i.ReadPara3_SetHead) for ReadPara3_SetHead
    setparabao.o(i.ReadPara_3) refers to setparabao.o(i.ReadPara3_SetEnd) for ReadPara3_SetEnd
    setparabao.o(i.ReadPara_3) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.ReadPara_3) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.ReadPara_4) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.ReadPara_4) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.ReadPara_4) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.ReadPara_4) refers to setparabao.o(i.ReadPara4_SetHead) for ReadPara4_SetHead
    setparabao.o(i.ReadPara_4) refers to setparabao.o(i.ReadPara4_SetEnd) for ReadPara4_SetEnd
    setparabao.o(i.ReadPara_4) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.ReadPara_4) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.RestoreFactory) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.RestoreFactory) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.RestoreFactory) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.RestoreFactory) refers to firmwareupdatefile.o(i.Drv_FlashErase) for Drv_FlashErase
    setparabao.o(i.RestoreFactory) refers to firmwareupdatefile.o(i.Drv_FlashWrite) for Drv_FlashWrite
    setparabao.o(i.RestoreFactory) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.RestoreFactory) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.RestoreFactory) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.RestoreFactory) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SaveParaToFlash) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SaveParaToFlash) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SaveParaToFlash) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SaveParaToFlash) refers to firmwareupdatefile.o(i.Drv_FlashErase) for Drv_FlashErase
    setparabao.o(i.SaveParaToFlash) refers to firmwareupdatefile.o(i.Drv_FlashWrite) for Drv_FlashWrite
    setparabao.o(i.SaveParaToFlash) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SaveParaToFlash) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SaveParaToFlash) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SaveParaToFlash) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SendPara_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SendPara_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SendPara_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetCoordToAlgorithm) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetCoordToAlgorithm) refers to setparabao.o(.constdata) for SetCoord
    setparabao.o(i.SetCoordToAlgorithm) refers to main.o(.bss) for combineData
    setparabao.o(i.SetParaAll) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaAll) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaAll) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaAll) refers to computerframeparse.o(i.comm_param_setbits) for comm_param_setbits
    setparabao.o(i.SetParaAll) refers to setparabao.o(i.SetCoordToAlgorithm) for SetCoordToAlgorithm
    setparabao.o(i.SetParaAll) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    setparabao.o(i.SetParaAll) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaAll) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaAll) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaAll) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaAll) refers to computerframeparse.o(.bss) for hSetting
    setparabao.o(i.SetParaAngle) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaAngle) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaAngle) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaAngle) refers to computerframeparse.o(i.comm_param_setbits) for comm_param_setbits
    setparabao.o(i.SetParaAngle) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    setparabao.o(i.SetParaAngle) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaAngle) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaAngle) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaAngle) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaAngle) refers to computerframeparse.o(.bss) for hSetting
    setparabao.o(i.SetParaBaud) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaBaud) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaBaud) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaBaud) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaBaud) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaBaud) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaBaud) refers to systick.o(i.delay_ms) for delay_ms
    setparabao.o(i.SetParaBaud) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    setparabao.o(i.SetParaBaud) refers to main.o(i.gd_eval_com_init) for gd_eval_com_init
    setparabao.o(i.SetParaBaud) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    setparabao.o(i.SetParaBaud) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaCalibration) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaCalibration) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaCalibration) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaCalibration) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaCalibration) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaCalibration) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaCalibration) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaCoord) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaCoord) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaCoord) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaCoord) refers to setparabao.o(i.SetCoordToAlgorithm) for SetCoordToAlgorithm
    setparabao.o(i.SetParaCoord) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaCoord) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaCoord) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaCoord) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaDataOutType) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaDataOutType) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaDataOutType) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaDataOutType) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaDataOutType) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaDataOutType) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaDataOutType) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaDataOutType) refers to main.o(.bss) for combineData
    setparabao.o(i.SetParaDebugMode) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaDebugMode) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaDebugMode) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaDebugMode) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaDebugMode) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaDebugMode) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaDebugMode) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaDebugMode) refers to main.o(.bss) for combineData
    setparabao.o(i.SetParaDeviation) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaDeviation) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaDeviation) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaDeviation) refers to computerframeparse.o(i.comm_param_setbits) for comm_param_setbits
    setparabao.o(i.SetParaDeviation) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    setparabao.o(i.SetParaDeviation) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaDeviation) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaDeviation) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaDeviation) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaDeviation) refers to computerframeparse.o(.bss) for hSetting
    setparabao.o(i.SetParaFactorAcc) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaFactorAcc) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaFactorAcc) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaFactorAcc) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaFactorAcc) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaFactorAcc) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaFactorAcc) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaFactorGyro) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaFactorGyro) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaFactorGyro) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaFactorGyro) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaFactorGyro) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaFactorGyro) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaFactorGyro) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaFilter) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaFilter) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaFilter) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaFilter) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaFilter) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaFilter) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaFilter) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaFrequency) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaFrequency) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaFrequency) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaFrequency) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaFrequency) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaFrequency) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaFrequency) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaFrequency) refers to computerframeparse.o(.bss) for hSetting
    setparabao.o(i.SetParaGnss) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaGnss) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaGnss) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaGnss) refers to computerframeparse.o(i.comm_param_setbits) for comm_param_setbits
    setparabao.o(i.SetParaGnss) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    setparabao.o(i.SetParaGnss) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaGnss) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaGnss) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaGnss) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaGnss) refers to computerframeparse.o(.bss) for hSetting
    setparabao.o(i.SetParaGnssInitValue) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaGnssInitValue) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaGnssInitValue) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaGnssInitValue) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaGnssInitValue) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaGnssInitValue) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaGnssInitValue) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaGpsType) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaGpsType) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaGpsType) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaGpsType) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaGpsType) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaGpsType) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaGpsType) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaGpsType) refers to main.o(.bss) for combineData
    setparabao.o(i.SetParaGyroType) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaGyroType) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaGyroType) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaGyroType) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaGyroType) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaGyroType) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaGyroType) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaGyroType) refers to main.o(.bss) for combineData
    setparabao.o(i.SetParaKalmanQ) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaKalmanQ) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaKalmanQ) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaKalmanQ) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaKalmanQ) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaKalmanQ) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaKalmanQ) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaKalmanR) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaKalmanR) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaKalmanR) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaKalmanR) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaKalmanR) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaKalmanR) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaKalmanR) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaTime) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaTime) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaTime) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaTime) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaTime) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaTime) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaTime) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaTime) refers to computerframeparse.o(.bss) for hSetting
    setparabao.o(i.SetParaUpdateEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaUpdateEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaUpdateEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaUpdateEnd) refers to setparabao.o(i.UpdateEnd_SetHead) for UpdateEnd_SetHead
    setparabao.o(i.SetParaUpdateEnd) refers to setparabao.o(i.UpdateEnd_SetEnd) for UpdateEnd_SetEnd
    setparabao.o(i.SetParaUpdateEnd) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaUpdateEnd) refers to systick.o(i.delay_ms) for delay_ms
    setparabao.o(i.SetParaUpdateEnd) refers to firmwareupdatefile.o(i.Drv_SystemReset) for Drv_SystemReset
    setparabao.o(i.SetParaUpdateEnd) refers to setparabao.o(.data) for g_UpdateFinishSuccessful
    setparabao.o(i.SetParaUpdateEnd) refers to firmwareupdatefile.o(.data) for g_StartUpdateFirm
    setparabao.o(i.SetParaUpdateSend) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaUpdateSend) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaUpdateSend) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaUpdateSend) refers to setparabao.o(i.ParaUpdateHandle) for ParaUpdateHandle
    setparabao.o(i.SetParaUpdateSend) refers to systick.o(i.delay_ms) for delay_ms
    setparabao.o(i.SetParaUpdateSend) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    setparabao.o(i.SetParaUpdateSend) refers to main.o(i.gd_eval_com_init) for gd_eval_com_init
    setparabao.o(i.SetParaUpdateSend) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    setparabao.o(i.SetParaUpdateSend) refers to setparabao.o(i.UpdateSend_SetHead) for UpdateSend_SetHead
    setparabao.o(i.SetParaUpdateSend) refers to setparabao.o(i.UpdateSend_SetEnd) for UpdateSend_SetEnd
    setparabao.o(i.SetParaUpdateSend) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaUpdateSend) refers to setparabao.o(.data) for g_UpdateBackFlag
    setparabao.o(i.SetParaUpdateSend) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaUpdateStart) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaUpdateStart) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaUpdateStart) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaUpdateStart) refers to systick.o(i.delay_ms) for delay_ms
    setparabao.o(i.SetParaUpdateStart) refers to setparabao.o(i.UpdateStart_SetHead) for UpdateStart_SetHead
    setparabao.o(i.SetParaUpdateStart) refers to setparabao.o(i.UpdateStart_SetEnd) for UpdateStart_SetEnd
    setparabao.o(i.SetParaUpdateStart) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaUpdateStart) refers to firmwareupdatefile.o(.data) for g_StartUpdateFirm
    setparabao.o(i.SetParaUpdateStart) refers to setparabao.o(.data) for g_UpdateSuccessful
    setparabao.o(i.SetParaUpdateStop) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaUpdateStop) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaUpdateStop) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaUpdateStop) refers to setparabao.o(i.UpdateStop_SetHead) for UpdateStop_SetHead
    setparabao.o(i.SetParaUpdateStop) refers to setparabao.o(i.UpdateStop_SetEnd) for UpdateStop_SetEnd
    setparabao.o(i.SetParaUpdateStop) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaUpdateStop) refers to firmwareupdatefile.o(.data) for g_StartUpdateFirm
    setparabao.o(i.SetParaVector) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.SetParaVector) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.SetParaVector) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.SetParaVector) refers to computerframeparse.o(i.comm_param_setbits) for comm_param_setbits
    setparabao.o(i.SetParaVector) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    setparabao.o(i.SetParaVector) refers to setparabao.o(i.SendPara_SetHead) for SendPara_SetHead
    setparabao.o(i.SetParaVector) refers to setparabao.o(i.SendPara_SetEnd) for SendPara_SetEnd
    setparabao.o(i.SetParaVector) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    setparabao.o(i.SetParaVector) refers to setparabao.o(.bss) for stSetPara
    setparabao.o(i.SetParaVector) refers to computerframeparse.o(.bss) for hSetting
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaBaud) for SetParaBaud
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaFrequency) for SetParaFrequency
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaGnss) for SetParaGnss
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaAngle) for SetParaAngle
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaVector) for SetParaVector
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaDeviation) for SetParaDeviation
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaGnssInitValue) for SetParaGnssInitValue
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaCoord) for SetParaCoord
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaTime) for SetParaTime
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SaveParaToFlash) for SaveParaToFlash
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.RestoreFactory) for RestoreFactory
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaAll) for SetParaAll
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.ReadPara) for ReadPara
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaGpsType) for SetParaGpsType
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaDataOutType) for SetParaDataOutType
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaDebugMode) for SetParaDebugMode
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaGyroType) for SetParaGyroType
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaCalibration) for SetParaCalibration
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaKalmanQ) for SetParaKalmanQ
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaKalmanR) for SetParaKalmanR
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaFilter) for SetParaFilter
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaFactorGyro) for SetParaFactorGyro
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaFactorAcc) for SetParaFactorAcc
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaUpdateStart) for SetParaUpdateStart
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaUpdateSend) for SetParaUpdateSend
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaUpdateEnd) for SetParaUpdateEnd
    setparabao.o(i.UartDmaRecSetPara) refers to setparabao.o(i.SetParaUpdateStop) for SetParaUpdateStop
    setparabao.o(i.UpdateEnd_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.UpdateEnd_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.UpdateEnd_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.UpdateSend_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.UpdateSend_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.UpdateSend_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.UpdateStart_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.UpdateStart_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.UpdateStart_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    setparabao.o(i.UpdateStop_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    setparabao.o(i.UpdateStop_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    setparabao.o(i.UpdateStop_SetEnd) refers to firmwareupdatefile.o(i.crc_verify_8bit) for crc_verify_8bit
    asin.o(i.__hardfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.__hardfp_asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.__hardfp_asin) refers to dadd.o(.text) for __aeabi_dadd
    asin.o(i.__hardfp_asin) refers to errno.o(i.__set_errno) for __set_errno
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.__hardfp_asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.__hardfp_asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.__hardfp_asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.__hardfp_asin) refers to fabs.o(i.fabs) for fabs
    asin.o(i.__hardfp_asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.__hardfp_asin) refers to asin.o(.constdata) for .constdata
    asin.o(i.__softfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(i.asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.____hardfp_asin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    asin_x.o(i.____hardfp_asin$lsc) refers to dadd.o(.text) for __aeabi_dadd
    asin_x.o(i.____hardfp_asin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    asin_x.o(i.____hardfp_asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.____hardfp_asin$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    asin_x.o(i.____hardfp_asin$lsc) refers to fabs.o(i.fabs) for fabs
    asin_x.o(i.____hardfp_asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.____hardfp_asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2f.o(i.__hardfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to errno.o(i.__set_errno) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atof.o(i.__hardfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__hardfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.__hardfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__softfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    exp.o(i.__hardfp_exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    exp.o(i.__hardfp_exp) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    exp.o(i.__hardfp_exp) refers to errno.o(i.__set_errno) for __set_errno
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    exp.o(i.__hardfp_exp) refers to cdcmple.o(.text) for __aeabi_cdcmple
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    exp.o(i.__hardfp_exp) refers to dadd.o(.text) for __aeabi_dsub
    exp.o(i.__hardfp_exp) refers to dmul.o(.text) for __aeabi_dmul
    exp.o(i.__hardfp_exp) refers to dfixi.o(.text) for __aeabi_d2iz
    exp.o(i.__hardfp_exp) refers to dflti.o(.text) for __aeabi_i2d
    exp.o(i.__hardfp_exp) refers to poly.o(i.__kernel_poly) for __kernel_poly
    exp.o(i.__hardfp_exp) refers to ddiv.o(.text) for __aeabi_ddiv
    exp.o(i.__hardfp_exp) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    exp.o(i.__hardfp_exp) refers to exp.o(.constdata) for .constdata
    exp.o(i.__softfp_exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.__softfp_exp) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    exp.o(i.exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.exp) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    exp.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____hardfp_exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____hardfp_exp$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    exp_x.o(i.____hardfp_exp$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    exp_x.o(i.____hardfp_exp$lsc) refers to errno.o(i.__set_errno) for __set_errno
    exp_x.o(i.____hardfp_exp$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    exp_x.o(i.____hardfp_exp$lsc) refers to dadd.o(.text) for __aeabi_dsub
    exp_x.o(i.____hardfp_exp$lsc) refers to dmul.o(.text) for __aeabi_dmul
    exp_x.o(i.____hardfp_exp$lsc) refers to dfixi.o(.text) for __aeabi_d2iz
    exp_x.o(i.____hardfp_exp$lsc) refers to dflti.o(.text) for __aeabi_i2d
    exp_x.o(i.____hardfp_exp$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    exp_x.o(i.____hardfp_exp$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    exp_x.o(i.____hardfp_exp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    exp_x.o(i.____hardfp_exp$lsc) refers to exp_x.o(.constdata) for .constdata
    exp_x.o(i.____softfp_exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____softfp_exp$lsc) refers to exp_x.o(i.____hardfp_exp$lsc) for ____hardfp_exp$lsc
    exp_x.o(i.__exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.__exp$lsc) refers to exp_x.o(i.____hardfp_exp$lsc) for ____hardfp_exp$lsc
    exp_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__hardfp_floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__hardfp_floor) refers to dadd.o(.text) for __aeabi_dadd
    floor.o(i.__hardfp_floor) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    floor.o(i.__softfp_floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__softfp_floor) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    floor.o(i.floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.floor) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    fmod.o(i.__hardfp_fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.__hardfp_fmod) refers to drem.o(.text) for _drem
    fmod.o(i.__hardfp_fmod) refers to dadd.o(.text) for __aeabi_drsub
    fmod.o(i.__hardfp_fmod) refers to errno.o(i.__set_errno) for __set_errno
    fmod.o(i.__hardfp_fmod) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    fmod.o(i.__softfp_fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.__softfp_fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod.o(i.fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod_x.o(i.____hardfp_fmod$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod_x.o(i.____hardfp_fmod$lsc) refers to drem.o(.text) for _drem
    fmod_x.o(i.____hardfp_fmod$lsc) refers to dadd.o(.text) for __aeabi_drsub
    fmod_x.o(i.____hardfp_fmod$lsc) refers to errno.o(i.__set_errno) for __set_errno
    fmod_x.o(i.____softfp_fmod$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod_x.o(i.____softfp_fmod$lsc) refers to drem.o(.text) for _drem
    fmod_x.o(i.____softfp_fmod$lsc) refers to dadd.o(.text) for __aeabi_drsub
    fmod_x.o(i.____softfp_fmod$lsc) refers to errno.o(i.__set_errno) for __set_errno
    fmod_x.o(i.__fmod$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod_x.o(i.__fmod$lsc) refers to drem.o(.text) for _drem
    fmod_x.o(i.__fmod$lsc) refers to dadd.o(.text) for __aeabi_drsub
    fmod_x.o(i.__fmod$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__hardfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to errno.o(i.__set_errno) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    strtod.o(i.__hardfp_strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.__hardfp_strtod) refers to strtod.o(.text) for __strtod_int
    strtod.o(i.__softfp_strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.__softfp_strtod) refers to strtod.o(.text) for __strtod_int
    strtod.o(i.strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.strtod) refers to strtod.o(.text) for __strtod_int
    tan.o(i.__hardfp_tan) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan.o(i.__hardfp_tan) refers to errno.o(i.__set_errno) for __set_errno
    tan.o(i.__hardfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__hardfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__hardfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__hardfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.__softfp_tan) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan.o(i.__softfp_tan) refers to errno.o(i.__set_errno) for __set_errno
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__softfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__softfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.tan) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan.o(i.tan) refers to errno.o(i.__set_errno) for __set_errno
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____hardfp_tan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_x.o(i.____hardfp_tan$lsc) refers to errno.o(i.__set_errno) for __set_errno
    tan_x.o(i.____hardfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____hardfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____hardfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____softfp_tan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_x.o(i.____softfp_tan$lsc) refers to errno.o(i.__set_errno) for __set_errno
    tan_x.o(i.____softfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____softfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____softfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.__tan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_x.o(i.__tan$lsc) refers to errno.o(i.__set_errno) for __set_errno
    tan_x.o(i.__tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.__tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.__tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    localtime.o(.text) refers to localtime.o(.bss) for .bss
    localtime.o(.text) refers to localtime.o(.constdata) for .constdata
    mktime.o(.text) refers to localtime_i.o(.text) for _localtime
    mktime.o(.text) refers to mktime.o(.constdata) for .constdata
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    atol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atol.o(.text) refers to strtol.o(.text) for strtol
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    cos_i.o(i.__kernel_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfixi.o(.text) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(.text) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to dadd.o(.text) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to dadd.o(.text) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(.text) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfixi.o(.text) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflti.o(.text) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to dfltui.o(.text) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(.text) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to dadd.o(.text) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to dadd.o(.text) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers to dfixi.o(.text) for __aeabi_d2iz
    tan_i.o(i.__kernel_tan) refers to dadd.o(.text) for __aeabi_drsub
    tan_i.o(i.__kernel_tan) refers to dmul.o(.text) for __aeabi_dmul
    tan_i.o(i.__kernel_tan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i.o(i.__kernel_tan) refers to dflti.o(.text) for __aeabi_i2d
    tan_i.o(i.__kernel_tan) refers to ddiv.o(.text) for __aeabi_ddiv
    tan_i.o(i.__kernel_tan) refers to fabs.o(i.fabs) for fabs
    tan_i.o(i.__kernel_tan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    tan_i.o(i.__kernel_tan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    tan_i.o(i.__kernel_tan) refers to tan_i.o(.constdata) for .constdata
    tan_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers to dfixi.o(.text) for __aeabi_d2iz
    tan_i_x.o(i.____kernel_tan$lsc) refers to dadd.o(.text) for __aeabi_drsub
    tan_i_x.o(i.____kernel_tan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    tan_i_x.o(i.____kernel_tan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i_x.o(i.____kernel_tan$lsc) refers to dflti.o(.text) for __aeabi_i2d
    tan_i_x.o(i.____kernel_tan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    tan_i_x.o(i.____kernel_tan$lsc) refers to fabs.o(i.fabs) for fabs
    tan_i_x.o(i.____kernel_tan$lsc) refers to tan_i_x.o(.constdata) for .constdata
    tan_i_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    localtime_w.o(.text) refers to localtime_i.o(.text) for _localtime
    localtime_w.o(.text) refers to localtime_w.o(.bss) for .bss
    localtime_i.o(.text) refers to localtime_i.o(.constdata) for .constdata
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_o.o(.text) for isspace
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_o.o(.text) for isspace
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    drem.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp

